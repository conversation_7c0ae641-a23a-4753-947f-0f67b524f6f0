html
    head
        title T<PERSON><PERSON> kho<PERSON>n
        link(rel="stylesheet", href="/public/css/main.css")
        link(rel="stylesheet", href="/public/css/style.css")
        link(rel="stylesheet", href="/public/css/responsive.css")
        link(rel="stylesheet", href="/public/css/col.css")
        script(src='/public/js/logout.js')
        link(rel="stylesheet", href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css")
    body
        include ../header.pug

        div(id="customer")
            div(class="customer-left")
                div
                    div
                        //-Thay thẻ a sang thẻ button
                        ul
                            li: a(href="/customer") Thông tin tài khoản
                            li: a(href="/customer/address") Thông tin địa chỉ
                            li(class="active"): a(href="/customer/change-password") Đ<PERSON>i mật khẩu
                            li: a(href="/customer/orders") Đơn hàng của bạn
                            li(class="active" data-user-id=user._id): a(href="/customer/notifications") Thông báo
                        button(class="log-out") Đăng xuất
            div(class="customer-right")
                h1 Đổi mật khẩu
                div
                    if error
                        div(class="alert-error")= error
                    if success
                        div(class="alert-success")= success
                    form(class="customer-form", action="/auth/change-password", method="POST")
                        div(class="form-info")
                            p Mật khẩu hiện tại
                            input(type="password", name="currentPassword", placeholder="Nhập mật khẩu cũ", required)
                        div(class="form-info")
                            p Mật khẩu mới
                            input(type="password", id="newPassword", name="newPassword", placeholder="Nhập mật khẩu mới", required, minlength="8")
                        div(class="form-info")
                            p Nhập lại mật khẩu mới
                            input(type="password", id="confirmPassword", name="confirmPassword", placeholder="Nhập lại mật khẩu mới", required)
                        div(class="form-info")
                            button(class="create-update", type="submit") Cập nhật

        include ../logout.pug
        include ../suggest.pug
        include ../footer.pug
        script.
            // Password validation
            document.addEventListener('DOMContentLoaded', function() {
                const form = document.querySelector('form');
                const newPassword = document.getElementById('newPassword');
                const confirmPassword = document.getElementById('confirmPassword');

                form.addEventListener('submit', function(e) {
                    if (newPassword.value !== confirmPassword.value) {
                        e.preventDefault();
                        alert('Mật khẩu xác nhận không khớp với mật khẩu mới!');
                    }
                });
            });