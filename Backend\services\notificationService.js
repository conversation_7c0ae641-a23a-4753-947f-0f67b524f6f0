const Notification = require('../models/notificationModel');

class NotificationService {
  constructor(io) {
    this.io = io;
  }

  // Tạo và gửi thông báo đơn hàng mới cho admin
  async createNewOrderNotification(order, user) {
    try {
      const notification = new Notification({
        type: 'new_order',
        title: 'Đơn hàng mới',
        message: `${user.username} đã đặt đơn hàng ${order.orderCode} với tổng tiền ${order.totalPrice.toLocaleString()}đ`,
        data: {
          orderId: order._id,
          userId: user._id,
          amount: order.totalPrice,
          orderCode: order.orderCode
        },
        targetRole: 'admin',
        priority: 'high'
      });

      await notification.save();

      // Gửi thông báo real-time đến admin
      this.io.to('admin-room').emit('new-notification', {
        id: notification._id,
        type: notification.type,
        title: notification.title,
        message: notification.message,
        data: notification.data,
        priority: notification.priority,
        createdAt: notification.createdAt
      });

      console.log(`Đ<PERSON> gửi thông báo đơn hàng mới cho admin: ${order.orderCode}`);
      return notification;
    } catch (error) {
      console.error('Lỗi tạo thông báo đơn hàng mới:', error);
    }
  }

  // Tạo thông báo cho người dùng khi đặt hàng thành công
  async createOrderConfirmationNotification(order, user) {
    try {
      const notification = new Notification({
        type: 'order_confirmation',
        title: 'Đặt hàng thành công',
        message: `Đơn hàng ${order.orderCode} của bạn đã được đặt thành công với tổng tiền ${order.totalPrice.toLocaleString()}đ. Chúng tôi sẽ xử lý đơn hàng trong thời gian sớm nhất.`,
        data: {
          orderId: order._id,
          userId: user._id,
          amount: order.totalPrice,
          orderCode: order.orderCode
        },
        targetRole: 'user',
        targetUserId: user._id,
        priority: 'medium'
      });

      await notification.save();

      // Gửi thông báo real-time đến user (nếu đang online)
      this.io.to(`user-${user._id}`).emit('new-notification', {
        id: notification._id,
        type: notification.type,
        title: notification.title,
        message: notification.message,
        data: notification.data,
        priority: notification.priority,
        createdAt: notification.createdAt
      });

      console.log(`Đã gửi thông báo xác nhận đơn hàng cho user: ${user.username}`);
      return notification;
    } catch (error) {
      console.error('Lỗi tạo thông báo xác nhận đơn hàng:', error);
    }
  }

  // Tạo thông báo khi đơn hàng bị hủy
  async createOrderCancellationNotification(order, user, reason = '') {
    try {
      const notification = new Notification({
        type: 'order_cancelled',
        title: 'Đơn hàng đã bị hủy',
        message: `Đơn hàng ${order.orderCode} của bạn đã bị hủy. ${reason ? `Lý do: ${reason}` : 'Vui lòng liên hệ với chúng tôi nếu có thắc mắc.'}`,
        data: {
          orderId: order._id,
          userId: user._id,
          amount: order.totalPrice,
          orderCode: order.orderCode,
          reason: reason
        },
        targetRole: 'user',
        targetUserId: user._id,
        priority: 'high'
      });

      await notification.save();

      // Gửi thông báo real-time đến user
      this.io.to(`user-${user._id}`).emit('new-notification', {
        id: notification._id,
        type: notification.type,
        title: notification.title,
        message: notification.message,
        data: notification.data,
        priority: notification.priority,
        createdAt: notification.createdAt
      });

      console.log(`Đã gửi thông báo hủy đơn hàng cho user: ${user.username}`);
      return notification;
    } catch (error) {
      console.error('Lỗi tạo thông báo hủy đơn hàng:', error);
    }
  }

  // Tạo thông báo hủy đơn hàng cho admin
  async createOrderCancelledNotification(order, user) {
    try {
      const notification = new Notification({
        type: 'order_cancelled',
        title: 'Đơn hàng bị hủy',
        message: `${user.username} đã hủy đơn hàng ${order.orderCode}`,
        data: {
          orderId: order._id,
          userId: user._id,
          amount: order.totalPrice,
          orderCode: order.orderCode
        },
        targetRole: 'admin',
        priority: 'medium'
      });

      await notification.save();

      this.io.to('admin-room').emit('new-notification', {
        id: notification._id,
        type: notification.type,
        title: notification.title,
        message: notification.message,
        data: notification.data,
        priority: notification.priority,
        createdAt: notification.createdAt
      });

      console.log(`Đã gửi thông báo hủy đơn hàng cho admin: ${order.orderCode}`);
      return notification;
    } catch (error) {
      console.error('Lỗi tạo thông báo hủy đơn hàng:', error);
    }
  }

  // Tạo thông báo cập nhật trạng thái đơn hàng cho người dùng
  async createOrderStatusUpdateNotification(order, user, newStatus, oldStatus) {
    try {
      let title, message;

      switch(newStatus) {
        case 'confirmed':
          title = 'Đơn hàng đã được xác nhận';
          message = `Đơn hàng ${order.orderCode} của bạn đã được xác nhận và đang được chuẩn bị.`;
          break;
        case 'transporting':
          title = 'Đơn hàng đang được giao';
          message = `Đơn hàng ${order.orderCode} của bạn đang trên đường giao đến địa chỉ của bạn.`;
          break;
        case 'delivered':
          title = 'Đơn hàng đã được giao thành công';
          message = `Đơn hàng ${order.orderCode} đã được giao thành công. Cảm ơn bạn đã mua hàng!`;
          break;
        case 'cancelled':
          title = 'Đơn hàng đã bị hủy';
          message = `Đơn hàng ${order.orderCode} của bạn đã bị hủy. Nếu có thắc mắc, vui lòng liên hệ với chúng tôi.`;
          break;
        default:
          title = 'Cập nhật đơn hàng';
          message = `Trạng thái đơn hàng ${order.orderCode} đã được cập nhật.`;
      }

      const notification = new Notification({
        type: 'order_status_update',
        title: title,
        message: message,
        data: {
          orderId: order._id,
          userId: user._id,
          amount: order.totalPrice,
          orderCode: order.orderCode,
          newStatus: newStatus,
          oldStatus: oldStatus
        },
        targetRole: 'user',
        targetUserId: user._id,
        priority: newStatus === 'delivered' ? 'high' : 'medium'
      });

      await notification.save();

      // Gửi thông báo real-time đến user
      this.io.to(`user-${user._id}`).emit('new-notification', {
        id: notification._id,
        type: notification.type,
        title: notification.title,
        message: notification.message,
        data: notification.data,
        priority: notification.priority,
        createdAt: notification.createdAt
      });

      console.log(`Đã gửi thông báo cập nhật trạng thái đơn hàng cho user: ${user.username} - ${newStatus}`);
      return notification;
    } catch (error) {
      console.error('Lỗi tạo thông báo cập nhật trạng thái:', error);
    }
  }

  // Tạo thông báo sắp hết hàng
  async createLowStockNotification(book) {
    try {
      const notification = new Notification({
        type: 'low_stock',
        title: 'Sắp hết hàng',
        message: `Sách "${book.title}" chỉ còn ${book.stock} sản phẩm`,
        data: {
          bookId: book._id
        },
        targetRole: 'admin',
        priority: book.stock <= 2 ? 'urgent' : 'medium'
      });

      await notification.save();

      this.io.to('admin-room').emit('new-notification', {
        id: notification._id,
        type: notification.type,
        title: notification.title,
        message: notification.message,
        data: notification.data,
        priority: notification.priority,
        createdAt: notification.createdAt
      });

      console.log(`Đã gửi thông báo sắp hết hàng: ${book.title}`);
      return notification;
    } catch (error) {
      console.error('Lỗi tạo thông báo sắp hết hàng:', error);
    }
  }

  // Lấy danh sách thông báo
  async getNotifications(targetRole = 'admin', limit = 20, page = 1) {
    try {
      const skip = (page - 1) * limit;
      const notifications = await Notification.find({ targetRole })
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .populate('data.userId', 'username')
        .populate('data.orderId', 'orderCode')
        .populate('data.bookId', 'title');

      const total = await Notification.countDocuments({ targetRole });
      const unreadCount = await Notification.countDocuments({ targetRole, isRead: false });

      return {
        notifications,
        total,
        unreadCount,
        page,
        pages: Math.ceil(total / limit)
      };
    } catch (error) {
      console.error('Lỗi lấy danh sách thông báo:', error);
      return { notifications: [], total: 0, unreadCount: 0, page: 1, pages: 0 };
    }
  }

  // Đánh dấu đã đọc
  async markAsRead(notificationId) {
    try {
      await Notification.findByIdAndUpdate(notificationId, { isRead: true });
      return true;
    } catch (error) {
      console.error('Lỗi đánh dấu đã đọc:', error);
      return false;
    }
  }

  // Đánh dấu tất cả đã đọc
  async markAllAsRead(targetRole = 'admin') {
    try {
      await Notification.updateMany({ targetRole, isRead: false }, { isRead: true });
      return true;
    } catch (error) {
      console.error('Lỗi đánh dấu tất cả đã đọc:', error);
      return false;
    }
  }

  // Xóa thông báo cũ (chạy định kỳ)
  async cleanupOldNotifications(daysOld = 30) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);
      
      const result = await Notification.deleteMany({
        createdAt: { $lt: cutoffDate },
        isRead: true
      });
      
      console.log(`Đã xóa ${result.deletedCount} thông báo cũ`);
      return result.deletedCount;
    } catch (error) {
      console.error('Lỗi xóa thông báo cũ:', error);
      return 0;
    }
  }
}

module.exports = NotificationService;
