const User = require('../models/userModel');
const Book = require('../models/bookModel');
const Order = require('../models/orderModel');
const Category = require('../models/categoryModel');

// <PERSON><PERSON><PERSON> x<PERSON> lý lỗi chung
const handleError = (error, res, message) => {
  console.error(`Lỗi: ${message}`, error);
  res.status(500).json({
    success: false,
    message: message || 'Đã xảy ra lỗi',
    error: process.env.NODE_ENV === 'development' ? error.message : 'Internal Server Error'
  });
};

// Dashboard
const getDashboardSummary = async (req, res) => {
  try {
    // Lấy thông tin tổng quan
    const [totalUsers, totalBooks, totalOrders] = await Promise.all([
      User.countDocuments(),
      Book.countDocuments(),
      Order.countDocuments()
    ]);

    // Tính doanh thu
    const revenue = await Order.aggregate([
      { $match: { status: 'delivered' } },
      { $group: { _id: null, total: { $sum: '$totalPrice' } } }
    ]);

    const totalRevenue = revenue.length > 0 ? revenue[0].total : 0;

    res.render('admin/admin', {
      totalUsers,
      totalBooks,
      totalOrders,
      totalRevenue
    });
  } catch (error) {
    handleError(error, res, 'Không thể tải trang tổng quan');
  }
};

// Quản lý người dùng
const getAllUsers = async (req, res) => {
  try {
    const users = await User.find().select('-password');
    res.render('admin/user', { users });
  } catch (error) {
    handleError(error, res, 'Không thể tải danh sách người dùng');
  }
};

const getUserForEdit = async (req, res) => {
  try {
    const user = await User.findById(req.params.id).select('-password');

    if (!user) {
      return res.status(404).redirect('/admin/user?error=Không tìm thấy người dùng');
    }

    res.render('admin/edituser', { user });
  } catch (error) {
    handleError(error, res, 'Không thể tải thông tin người dùng');
  }
};

const updateUser = async (req, res) => {
  try {
    const { username, email, fullname, phone, role } = req.body;
    const userId = req.params.id;

    // Kiểm tra email và username có bị trùng không (trừ user hiện tại)
    const existingUser = await User.findOne({
      $and: [
        { _id: { $ne: userId } },
        { $or: [{ email }, { username }] }
      ]
    });

    if (existingUser) {
      const user = await User.findById(userId).select('-password');
      return res.render('admin/edituser', {
        user,
        error: 'Email hoặc tên đăng nhập đã tồn tại'
      });
    }

    const updateData = {
      username,
      email,
      fullname,
      phone,
      role
    };

    await User.findByIdAndUpdate(userId, updateData);
    res.redirect('/admin/user');
  } catch (error) {
    handleError(error, res, 'Không thể cập nhật người dùng');
  }
};

const deleteUser = async (req, res) => {
  try {
    await User.findByIdAndDelete(req.params.id);
    res.status(200).json({ success: true });
  } catch (error) {
    console.error('Lỗi khi xóa người dùng:', error);
    res.status(500).json({ success: false, message: 'Không thể xóa người dùng' });
  }
};

// Quản lý sách
const getAllBooks = async (req, res) => {
  try {
    const books = await Book.find().populate('category', 'name');
    res.render('admin/book', { books });
  } catch (error) {
    handleError(error, res, 'Không thể tải danh sách sách');
  }
};

const getAddBookForm = async (req, res) => {
  try {
    const categories = await Category.find();
    res.render('admin/addbook', { categories });
  } catch (error) {
    handleError(error, res, 'Không thể tải form thêm sách');
  }
};

const addBook = async (req, res) => {
  try {
    const {
      title, author, publisher, supplier, year, bookLanguage, format, pageCount,
      price, stock, sold, category, description, group
    } = req.body;

    // Tạo mã sách theo kiểu book01, book02, book03...
    const generateBookCode = async () => {
      // Tìm sách có mã code cao nhất
      const lastBook = await Book.findOne()
        .sort({ code: -1 })
        .select('code')
        .lean();

      let nextNumber = 1;

      if (lastBook && lastBook.code) {
        // Extract số từ mã code (ví dụ: book05 → 5)
        const match = lastBook.code.match(/book(\d+)/i);
        if (match && match[1]) {
          nextNumber = parseInt(match[1]) + 1;
        }
      }

      // Format: book + số có 2 chữ số (01, 02, 03...)
      return `book${String(nextNumber).padStart(2, '0')}`;
    };

    // Tạo mã sách
    const bookCode = await generateBookCode();

    // Lấy đường dẫn ảnh
    const imagePath = req.file ? `/uploads/${req.file.filename}` : '';

    const newBook = new Book({
      code: bookCode,
      title,
      author,
      publisher,
      supplier,
      year: year ? parseInt(year) : undefined,
      bookLanguage,
      format,
      pageCount: pageCount ? parseInt(pageCount) : undefined,
      price: parseFloat(price),
      stock: parseInt(stock),
      sold: 0, // Luôn bắt đầu từ 0, không cho phép chỉnh sửa
      category,
      description,
      group,
      image: imagePath
    });

    await newBook.save();
    console.log('Sách mới được tạo với mã:', newBook.code);
    res.redirect('/admin/book');
  } catch (error) {
    handleError(error, res, 'Không thể thêm sách mới');
  }
};

const getBookForEdit = async (req, res) => {
  try {
    const [book, categories] = await Promise.all([
      Book.findById(req.params.id),
      Category.find()
    ]);

    if (!book) {
      return res.status(404).redirect('/admin/book?error=Không tìm thấy sách');
    }

    res.render('admin/editbook', { book, categories });
  } catch (error) {
    handleError(error, res, 'Không thể tải thông tin sách');
  }
};

const updateBook = async (req, res) => {
  try {
    const {
      title, author, publisher, supplier, year, bookLanguage, format, pageCount,
      price, stock, category, description, group
    } = req.body;
    const bookId = req.params.id;

    const updateData = {
      title,
      author,
      publisher,
      supplier,
      year: year ? parseInt(year) : undefined,
      bookLanguage,
      format,
      pageCount: pageCount ? parseInt(pageCount) : undefined,
      price: parseFloat(price),
      stock: parseInt(stock),
      // Không cập nhật sold - giữ nguyên giá trị hiện tại
      category,
      description,
      group
    };

    // Nếu có file ảnh mới
    if (req.file) {
      updateData.image = `/uploads/${req.file.filename}`;
    }

    await Book.findByIdAndUpdate(bookId, updateData);
    res.redirect('/admin/book');
  } catch (error) {
    handleError(error, res, 'Không thể cập nhật sách');
  }
};

const deleteBook = async (req, res) => {
  try {
    await Book.findByIdAndDelete(req.params.id);
    res.status(200).json({ success: true });
  } catch (error) {
    console.error('Lỗi khi xóa sách:', error);
    res.status(500).json({ success: false, message: 'Không thể xóa sách' });
  }
};

// Quản lý danh mục
const getAllCategories = async (req, res) => {
  try {
    const categories = await Category.find();
    res.render('admin/category', { categories });
  } catch (error) {
    handleError(error, res, 'Không thể tải danh sách danh mục');
  }
};

const getCategoryForEdit = async (req, res) => {
  try {
    const category = await Category.findById(req.params.id);

    if (!category) {
      return res.status(404).redirect('/admin/category?error=Không tìm thấy danh mục');
    }

    res.render('admin/editcategory', { category });
  } catch (error) {
    handleError(error, res, 'Không thể tải thông tin danh mục');
  }
};

const addCategory = async (req, res) => {
  try {
    const { name } = req.body;

    if (!name) {
      return res.status(400).json({ success: false, message: 'Tên danh mục không được để trống' });
    }

    // Kiểm tra trùng lặp tên danh mục
    const existingCategory = await Category.findOne({ name: name.trim() });
    if (existingCategory) {
      return res.status(400).json({ success: false, message: 'Tên danh mục đã tồn tại' });
    }

    const newCategory = new Category({
      name: name.trim()
    });

    await newCategory.save();
    res.redirect('/admin/category');
  } catch (error) {
    // Xử lý lỗi duplicate key cho field code
    if (error.code === 11000 && error.message.includes('code_1')) {
      try {
        // Tự động fix bằng cách xóa index code_1
        const mongoose = require('mongoose');
        const db = mongoose.connection.db;
        const collection = db.collection('categories');

        // Xóa index code_1
        await collection.dropIndex('code_1');
        console.log('Đã xóa index code_1 tự động');

        // Xóa field code khỏi documents
        await collection.updateMany(
          { code: { $exists: true } },
          { $unset: { code: "" } }
        );
        console.log('Đã xóa field code khỏi documents');

        // Thử tạo category lại
        const newCategory = new Category({
          name: req.body.name.trim()
        });
        await newCategory.save();

        return res.redirect('/admin/category');
      } catch (fixError) {
        console.error('Lỗi khi tự động fix:', fixError);
        return res.status(500).json({
          success: false,
          message: 'Lỗi database đã được fix tự động, vui lòng thử lại'
        });
      }
    }

    handleError(error, res, 'Không thể thêm danh mục mới');
  }
};

const updateCategory = async (req, res) => {
  try {
    const { name } = req.body;

    if (!name) {
      return res.status(400).json({ success: false, message: 'Tên danh mục không được để trống' });
    }

    // Kiểm tra trùng lặp tên danh mục (trừ danh mục hiện tại)
    const existingCategory = await Category.findOne({
      name: name.trim(),
      _id: { $ne: req.params.id }
    });
    if (existingCategory) {
      const category = await Category.findById(req.params.id);
      return res.render('admin/editcategory', {
        category,
        error: 'Tên danh mục đã tồn tại'
      });
    }

    await Category.findByIdAndUpdate(req.params.id, { name: name.trim() });
    res.redirect('/admin/category');
  } catch (error) {
    // Xử lý lỗi duplicate key cho field code
    if (error.code === 11000 && error.message.includes('code_1')) {
      try {
        // Tự động fix bằng cách xóa index code_1
        const mongoose = require('mongoose');
        const db = mongoose.connection.db;
        const collection = db.collection('categories');

        // Xóa index code_1
        await collection.dropIndex('code_1');
        console.log('Đã xóa index code_1 tự động');

        // Xóa field code khỏi documents
        await collection.updateMany(
          { code: { $exists: true } },
          { $unset: { code: "" } }
        );
        console.log('Đã xóa field code khỏi documents');

        // Thử update lại
        await Category.findByIdAndUpdate(req.params.id, { name: req.body.name.trim() });
        return res.redirect('/admin/category');
      } catch (fixError) {
        console.error('Lỗi khi tự động fix:', fixError);
        const category = await Category.findById(req.params.id);
        return res.render('admin/editcategory', {
          category,
          error: 'Lỗi database đã được fix tự động, vui lòng thử lại'
        });
      }
    }

    handleError(error, res, 'Không thể cập nhật danh mục');
  }
};

const deleteCategory = async (req, res) => {
  try {
    // Kiểm tra xem danh mục có sách không
    const bookCount = await Book.countDocuments({ category: req.params.id });

    if (bookCount > 0) {
      return res.status(400).json({
        success: false,
        message: 'Không thể xóa danh mục này vì có sách thuộc danh mục'
      });
    }

    await Category.findByIdAndDelete(req.params.id);
    res.status(200).json({ success: true });
  } catch (error) {
    console.error('Lỗi khi xóa danh mục:', error);
    res.status(500).json({ success: false, message: 'Không thể xóa danh mục' });
  }
};

// Quản lý đơn hàng
const getAllOrders = async (req, res) => {
  try {
    const orders = await Order.find().populate('user', 'username').sort({ createdAt: -1 });

    // Nếu có orderId trong query, highlight đơn hàng đó
    const highlightOrderId = req.query.orderId || null;

    res.render('admin/order', {
      orders,
      highlightOrderId,
      success: req.query.success || null,
      error: req.query.error || null
    });
  } catch (error) {
    handleError(error, res, 'Không thể tải danh sách đơn hàng');
  }
};

const getOrderDetails = async (req, res) => {
  try {
    const order = await Order.findById(req.params.id)
      .populate('user', 'username email')
      .populate('items.book', 'title image price');

    if (!order) {
      return res.status(404).redirect('/admin/order?error=Không tìm thấy đơn hàng');
    }

    res.render('admin/orderdetail', { order });
  } catch (error) {
    handleError(error, res, 'Không thể tải chi tiết đơn hàng');
  }
};

const updateOrderStatus = async (req, res) => {
  try {
    const { status } = req.body;

    if (!['pending', 'transporting', 'delivered', 'cancelled'].includes(status)) {
      return res.redirect(`/admin/order?error=Trạng thái không hợp lệ`);
    }

    // Lấy đơn hàng hiện tại để kiểm tra trạng thái cũ
    const order = await Order.findById(req.params.id);
    if (!order) {
      return res.redirect('/admin/order?error=Không tìm thấy đơn hàng');
    }

    // Kiểm tra xem có thể thay đổi trạng thái không
    if (!order.canChangeStatusTo(status)) {
      return res.redirect(`/admin/order?error=Không thể thay đổi từ trạng thái "${order.status}" sang "${status}". Đơn hàng chỉ có thể thay đổi trạng thái một lần.`);
    }

    const oldStatus = order.status;

    // Sử dụng phương thức changeStatus từ model
    try {
      order.changeStatus(status, req.user ? req.user._id : null);
      await order.save();
    } catch (error) {
      return res.redirect(`/admin/order?error=${error.message}`);
    }

    // Xử lý thay đổi sold khi chuyển trạng thái
    if (oldStatus !== 'cancelled' && status === 'cancelled') {
      // Chuyển từ trạng thái khác sang cancelled: trừ sold, hoàn stock
      for (const item of order.items) {
        await Book.findByIdAndUpdate(
          item.book,
          {
            $inc: {
              stock: item.quantity,    // Hoàn trả stock
              sold: -item.quantity     // Trừ sold
            }
          }
        );
      }
      console.log(`Đã trừ sold khi hủy đơn hàng ${order.orderCode}`);
    }

    // Gửi thông báo cập nhật trạng thái cho user
    try {
      const io = req.app.get('io');
      if (io && oldStatus !== status) {
        const NotificationService = require('../services/notificationService');
        const notificationService = new NotificationService(io);

        // Populate user information
        const orderWithUser = await Order.findById(order._id).populate('user');
        if (orderWithUser && orderWithUser.user) {
          await notificationService.createOrderStatusUpdateNotification(
            orderWithUser,
            orderWithUser.user,
            status,
            oldStatus
          );
        }
      }
    } catch (notificationError) {
      console.error('Lỗi gửi thông báo cập nhật trạng thái:', notificationError);
      // Không làm gián đoạn quá trình cập nhật
    }

    res.redirect('/admin/order?success=Cập nhật trạng thái đơn hàng thành công');
  } catch (error) {
    console.error('Lỗi cập nhật trạng thái:', error);
    res.redirect('/admin/order?error=Không thể cập nhật trạng thái đơn hàng');
  }
};

const cancelOrder = async (req, res) => {
  try {
    const order = await Order.findById(req.params.id);

    if (!order) {
      return res.redirect('/admin/order?error=Không tìm thấy đơn hàng');
    }

    // Kiểm tra xem có thể hủy đơn hàng không
    if (!order.canChangeStatusTo('cancelled')) {
      return res.redirect('/admin/order?error=Không thể hủy đơn hàng này. Đơn hàng đã ở trạng thái cuối.');
    }

    // Sử dụng phương thức changeStatus từ model
    try {
      order.changeStatus('cancelled', req.user ? req.user._id : null);
      await order.save();
    } catch (error) {
      return res.redirect(`/admin/order?error=${error.message}`);
    }

    // Hoàn trả số lượng sách vào kho và trừ sold
    for (const item of order.items) {
      await Book.findByIdAndUpdate(
        item.book,
        {
          $inc: {
            stock: item.quantity,    // Hoàn trả stock
            sold: -item.quantity     // Trừ sold
          }
        }
      );
      console.log(`Đã hoàn trả ${item.quantity} sách ID ${item.book}: +stock, -sold`);
    }

    console.log(`Đã hủy đơn hàng ${order.orderCode}`);
    res.redirect('/admin/order?success=Đã hủy đơn hàng thành công');
  } catch (error) {
    console.error('Lỗi hủy đơn hàng:', error);
    res.redirect('/admin/order?error=Không thể hủy đơn hàng');
  }
};

// Quản lý doanh thu
const getSalesData = async (req, res) => {
  try {
    // Lấy dữ liệu doanh thu theo tháng
    const monthlyData = await Order.aggregate([
      { $match: { status: 'delivered' } },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          },
          revenue: { $sum: '$totalPrice' },
          orderCount: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': -1, '_id.month': -1 } },
      {
        $project: {
          _id: 0,
          month: {
            $concat: [
              { $toString: '$_id.month' },
              '/',
              { $toString: '$_id.year' }
            ]
          },
          revenue: 1,
          orderCount: 1
        }
      }
    ]);

    res.render('admin/sale', { monthlyData });
  } catch (error) {
    handleError(error, res, 'Không thể tải dữ liệu doanh thu');
  }
};

// Export tất cả các hàm
module.exports = {
  getDashboardSummary,
  getAllUsers,
  getUserForEdit,
  updateUser,
  deleteUser,
  getAllBooks,
  getAddBookForm,
  addBook,
  getBookForEdit,
  updateBook,
  deleteBook,
  getAllCategories,
  getCategoryForEdit,
  addCategory,
  updateCategory,
  deleteCategory,
  getAllOrders,
  getOrderDetails,
  updateOrderStatus,
  cancelOrder,
  getSalesData
};
