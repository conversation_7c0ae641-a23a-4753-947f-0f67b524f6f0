const NotificationService = require('../services/notificationService');

// L<PERSON>y danh sách thông báo
const getNotifications = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const io = req.app.get('io');
    const notificationService = new NotificationService(io);
    
    const result = await notificationService.getNotifications('admin', limit, page);
    
    res.json({
      success: true,
      ...result
    });
  } catch (error) {
    console.error('Lỗi lấy thông báo:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server khi lấy thông báo'
    });
  }
};

// Đánh dấu đã đọc
const markAsRead = async (req, res) => {
  try {
    const { id } = req.params;
    const io = req.app.get('io');
    const notificationService = new NotificationService(io);
    
    const success = await notificationService.markAsRead(id);
    
    if (success) {
      res.json({ success: true, message: 'Đ<PERSON> đánh dấu đã đọc' });
    } else {
      res.status(400).json({ success: false, message: 'Không thể đánh dấu đã đọc' });
    }
  } catch (error) {
    console.error('Lỗi đánh dấu đã đọc:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server'
    });
  }
};

// Đánh dấu tất cả đã đọc
const markAllAsRead = async (req, res) => {
  try {
    const io = req.app.get('io');
    const notificationService = new NotificationService(io);
    
    const success = await notificationService.markAllAsRead('admin');
    
    if (success) {
      res.json({ success: true, message: 'Đã đánh dấu tất cả đã đọc' });
    } else {
      res.status(400).json({ success: false, message: 'Không thể đánh dấu tất cả đã đọc' });
    }
  } catch (error) {
    console.error('Lỗi đánh dấu tất cả đã đọc:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server'
    });
  }
};

// Lấy số lượng thông báo chưa đọc
const getUnreadCount = async (req, res) => {
  try {
    const io = req.app.get('io');
    const notificationService = new NotificationService(io);
    
    const result = await notificationService.getNotifications('admin', 1, 1);
    
    res.json({
      success: true,
      unreadCount: result.unreadCount
    });
  } catch (error) {
    console.error('Lỗi lấy số thông báo chưa đọc:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server'
    });
  }
};

module.exports = {
  getNotifications,
  markAsRead,
  markAllAsRead,
  getUnreadCount
};
