/* Admin Notification Styles */
.notification-container {
  position: relative;
  display: inline-block;
  margin-right: 20px;
}

.notification-bell {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  position: relative;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.notification-bell:hover {
  background: rgba(255,255,255,0.1);
}

.notification-badge {
  position: absolute;
  top: 0;
  right: 0;
  background: #ff4757;
  color: white;
  border-radius: 50%;
  padding: 2px 6px;
  font-size: 12px;
  font-weight: bold;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.notification-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.15);
  width: 350px;
  max-height: 400px;
  overflow: hidden;
  z-index: 1000;
  display: none;
}

.notification-dropdown.show {
  display: block;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.notification-header {
  padding: 15px;
  border-bottom: 1px solid #eee;
  background: #f8f9fa;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.notification-header h4 {
  margin: 0;
  color: #333;
  font-size: 16px;
}

.notification-actions {
  display: flex;
  gap: 10px;
}

.btn-small {
  padding: 4px 8px;
  font-size: 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-mark-all {
  background: #007bff;
  color: white;
}

.btn-mark-all:hover {
  background: #0056b3;
}

.notification-list {
  max-height: 300px;
  overflow-y: auto;
}

.notification-item {
  padding: 12px 15px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s ease;
  display: flex;
  align-items: flex-start;
  gap: 10px;
}

.notification-item:hover {
  background: #f8f9fa;
}

.notification-item.unread {
  background: #e3f2fd;
  border-left: 3px solid #2196f3;
}

.notification-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: white;
  flex-shrink: 0;
}

.notification-icon.new-order {
  background: #4caf50;
}

.notification-icon.order-cancelled {
  background: #f44336;
}

.notification-icon.low-stock {
  background: #ff9800;
}

.notification-icon.system {
  background: #2196f3;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
  font-size: 14px;
}

.notification-message {
  color: #666;
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 4px;
}

.notification-time {
  color: #999;
  font-size: 11px;
}

.notification-empty {
  padding: 30px 15px;
  text-align: center;
  color: #999;
  font-style: italic;
}

.notification-footer {
  padding: 10px 15px;
  border-top: 1px solid #eee;
  background: #f8f9fa;
  text-align: center;
}

.btn-view-all {
  color: #007bff;
  text-decoration: none;
  font-size: 13px;
  font-weight: 500;
}

.btn-view-all:hover {
  text-decoration: underline;
}

/* Loading state */
.notification-loading {
  padding: 20px;
  text-align: center;
  color: #666;
}

.notification-loading::after {
  content: '';
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #ddd;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
  .notification-dropdown {
    width: 300px;
    right: -50px;
  }
  
  .notification-container {
    margin-right: 10px;
  }
}

@media (max-width: 480px) {
  .notification-dropdown {
    width: 280px;
    right: -100px;
  }
}
