html
    head
        title T<PERSON><PERSON>
        link(rel="stylesheet", href="/public/css/main.css")
        link(rel="stylesheet", href="/public/css/style.css")
        link(rel="stylesheet", href="/public/css/responsive.css")
        link(rel="stylesheet", href="/public/css/col.css")
        script(src='/public/js/logout.js')
        link(rel="stylesheet", href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css")
    body
        include ../../header.pug
        div(id="customer-header")
            //-Quay lại trang chủ
            div
                button(class="fa-solid fa-arrow-left")
                P <PERSON>ài khoản
        div(id="customer")
            div(class="customer-left")
                div
                    div
                        //-Thay thẻ a thành thẻ button
                        ul
                            li: a(href="#") Thông tin tài khoản
                            li: a(href="#") Thông tin địa chỉ
                            li: a(href="#") Đ<PERSON>i mật khẩu
                            li(class="active"): a(href="#") Đơn hàng của bạn
                            li(class="active" data-user-id=user._id): a(href="/customer/notifications") Thông báo
                        button(class="log-out") Đăng xuất
            div(class="customer-right")
                h1 Đơn hàng của bạn
                div 
                    div 
                        input(type="text" class="order-search" placeholder="Tìm kiếm...")  
                    div(class="order")
                        div(class="prd-order")
                            ul
                                li: a(href="#") Tất cả
                                li: a(href="#") Chờ xác nhận
                                li: a(href="#") Chờ giao hàng
                                li(class="active"): a(href="#") Đã giao  
                                li: a(href="#") Đã hủy  
                    div 
                        div(class="prd")
                            div 
                                div
                                    i(class="fa-solid fa-truck")
                                    p Giao hàng thành công
                                div Hoàn thành
                            div 
                                img(src="./public/img/1.jpg", alt="Ảnh")
                                div(class="prd-info")
                                    h2 Tuổi trẻ đáng giá bao nhiêu aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa 
                                    div
                                        p x1
                                        p 56.000 đ
                            div
                                p Tổng tiền: 
                                    span 56.000 đ
                                //-Chỉ hiện thị nút đánh giá ở sản phẩm giao thành công
                                button(class="btn-review") Đánh giá
                                button(class="btn-buy") Mua lại

                        div(class="prd")
                            div 
                                div
                                    i(class="fa-solid fa-truck")
                                    p Giao hàng thành công
                                div Hoàn thành
                            div 
                                img(src="./public/img/1.jpg", alt="Ảnh")
                                div(class="prd-info")
                                    h2 Tuổi trẻ đáng giá bao nhiêu aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa 
                                    div
                                        p x1
                                        p 56.000 đ
                            div
                                p Tổng tiền: 
                                    span 56.000 đ
                                //-Chỉ hiện thị nút đánh giá ở sản phẩm giao thành công
                                button(class="btn-review") Đánh giá
                                button(class="btn-buy") Mua lại
                        div(class="prd")
                            div 
                                div
                                    i(class="fa-solid fa-truck")
                                    p Giao hàng thành công
                                div Hoàn thành
                            div 
                                img(src="./public/img/1.jpg", alt="Ảnh")
                                div(class="prd-info")
                                    h2 Tuổi trẻ đáng giá bao nhiêu aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa 
                                    div
                                        p x1
                                        p 56.000 đ
                            div
                                p Tổng tiền: 
                                    span 56.000 đ
                                //-Chỉ hiện thị nút đánh giá ở sản phẩm giao thành công
                                button(class="btn-review") Đánh giá
                                button(class="btn-buy") Mua lại
                    div(class="listpage")
                        ul 
                            li: a(href=""): i(class="fa-solid fa-chevron-left")         
                            li(class="active"): a(href="") 1        
                            li: a(href="") 2        
                            li: a(href="") 3        
                            li: a(href="") ...        
                            li: a(href=""): i(class="fa-solid fa-chevron-right")
        //-Form đánh giá
        div(class="review")
            form(action="")
                div
                    input( class="star star-5" id="star-5" type="radio" name="star")
                    label( class="star star-5" for="star-5")
                    input( class="star star-4" id="star-4" type="radio" name="star")
                    label( class="star star-4" for="star-4")
                    input( class="star star-3" id="star-3" type="radio" name="star")
                    label( class="star star-3" for="star-3")
                    input( class="star star-2" id="star-2" type="radio" name="star")
                    label( class="star star-2" for="star-2")
                    input( class="star star-1" id="star-1" type="radio" name="star")
                    label( class="star star-1" for="star-1") 
                input(type="text" name="name" required class="form-name" placeholder="Nhập tên sẽ hiển thị khi đánh giá" )
                textarea(type="text" name="note" required class="form-note" placeholder="Nhập nhận xét của bạn" )
                div(class="btn")
                    button(class="cancel") Hủy 
                    button(class="review-mess") Gửi đánh giá
        include ../../logout.pug
        include ../../suggest.pug
        include ../../footer.pug
    script.
        const reviewBtn = document.querySelector(".btn-review");
        const review = document.querySelector(".review");
        const cancelBtn = document.querySelector(".cancel");
        const messBtn = document.querySelector(".review-mess");

        reviewBtn.addEventListener("click", function () {
            review.classList.add("open");
        });

        cancelBtn.addEventListener("click", function () {
            review.classList.remove("open");
        });

        messBtn.addEventListener("click", function () {
            review.classList.remove("open");
        });
               
        