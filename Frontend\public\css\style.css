header {
  background: white;
  padding: 15px 20px; /* K<PERSON>ảng cách bên trong header */
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1); /* Bóng nhẹ cho header */
  border-bottom: 1px solid #e9ecef; /* Đường viền dưới nhẹ */
}

.container {
  width: 100%;
  max-width: 1280px; /* Chiều rộng tối đa cho container */
  margin: 0 auto; /* Căn giữa container */
}

.content__header {
  display: flex;
  align-items: center;
}

.logo__header img {
  width: 80px; /* Chiều rộng logo */
  height: auto; /* <PERSON>ều cao tự động */
  margin-right: 50px; /* Khoảng cách giữa logo và menu */
}

.menu__header nav ul {
  display: flex;
  list-style-type: none; /* Xóa dấu chấm */
  padding: 0;
  margin: 0;
}

.menu__header nav ul li {
  margin: 0 15px; /* K<PERSON>ảng cách gi<PERSON>a các mục menu */
}

.menu__header nav ul li a {
  /*chữchữ*/
  text-decoration: none; /* Xóa gạch chân */
  color: #333; /* <PERSON><PERSON><PERSON> chữ tối cho nền trắng */
  font-size: 16px;
  padding: 10px 15px; /* Khoảng cách bên trong liên kết */
  border-radius: 5px; /* Bo tròn góc cho liên kết */
  transition: background 0.3s, color 0.3s; /* Thêm hiệu ứng chuyển tiếp */
}

.menu__header nav ul li a:hover {
  background-color: #f8f9fa; /* Màu nền hover nhẹ */
  color: #007bff; /* Màu chữ xanh khi hover */
}

.card__head {
  display: flex;
  align-items: center; /* Căn giữa theo chiều dọc */
}

.card {
  position: relative; /* Vị trí tương đối cho icon giỏ hàng */
  margin-left: 20px; /* Khoảng cách giữa user icon và giỏ hàng */
}

.card i {
  width: 35px;
  font-size: 25px;
  height: auto;
  color: #333; /* Màu tối cho nền trắng */
  cursor: pointer;
  transition: color 0.3s; /* Hiệu ứng chuyển màu */
}

.card i:hover {
  color: #007bff; /* Màu xanh khi hover */
}

.card__head > a i {
  font-size: 25px;
  color: #333; /* Màu tối cho nền trắng */
  transition: color 0.3s; /* Hiệu ứng chuyển màu */
}

.card__head > a i:hover {
  color: #007bff; /* Màu xanh khi hover */
}

#cart-count {
  position: absolute; /* Vị trí tuyệt đối cho số lượng trong giỏ hàng */
  top: -5px; /* Đặt ở phía trên cùng bên trái */
  right: -5px; /* Đặt ở phía bên phải */
  background: #ff1493; /* Màu nền cho số lượng */
  color: white;
  border-radius: 50%; /* Bo tròn */
  padding: 2px 5px; /* Khoảng cách bên trong */
  font-size: 11px; /* Kích thước chữ */
}

/* ===== CATEGORY PAGE STYLES ===== */
.books-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 20px;
  padding: 20px 0;
}

.book-card {
  text-decoration: none;
  color: inherit;
  display: block;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.book-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.book-item {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid #f0f0f0;
}

.book-image {
  width: 100%;
  height: 280px;
  overflow: hidden;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.book-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.book-card:hover .book-image img {
  transform: scale(1.03);
}

.book-info {
  padding: 15px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 120px;
}

.book-title {
  font-size: 15px;
  font-weight: 600;
  margin: 0 0 10px 0;
  line-height: 1.4;
  color: #333;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  height: 2.8em;
}

.book-price {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: auto;
}

.price {
  font-size: 16px;
  font-weight: 700;
  color: #e74c3c;
}

.sold {
  font-size: 11px;
  color: #666;
  background: #f1f2f6;
  padding: 3px 8px;
  border-radius: 10px;
  align-self: flex-start;
  white-space: nowrap;
}

.no-products {
  text-align: center;
  padding: 60px 20px;
  color: #666;
  font-size: 16px;
  grid-column: 1 / -1;
}

/* ===== STOCK STATUS STYLES ===== */
.out-of-stock {
  color: #e74c3c;
  font-weight: bold;
  margin: 10px 0;
}

.low-stock {
  color: #f39c12;
  font-weight: bold;
  margin: 10px 0;
}

.in-stock {
  color: #27ae60;
  margin: 10px 0;
}

.out-of-stock-message {
  padding: 20px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 5px;
  text-align: center;
  margin: 20px 0;
}

.out-of-stock-message h4 {
  color: #e74c3c;
  margin-bottom: 10px;
}

.out-of-stock-message p {
  color: #6c757d;
  margin: 0;
}

.out-of-stock-mobile {
  background: #f8f9fa;
  padding: 15px;
  text-align: center;
}

.out-of-stock-mobile p {
  color: #e74c3c;
  font-weight: bold;
  margin: 0;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
  .books-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 18px;
  }
}

@media (max-width: 768px) {
  .books-grid {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 15px;
    padding: 15px 0;
  }

  .book-image {
    height: 220px;
  }

  .book-info {
    padding: 12px;
    min-height: 100px;
  }

  .book-title {
    font-size: 14px;
  }

  .price {
    font-size: 15px;
  }

  .sold {
    font-size: 10px;
    padding: 2px 6px;
  }
}

@media (max-width: 480px) {
  .books-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .book-image {
    height: 180px;
  }

  .book-info {
    padding: 10px;
    min-height: 90px;
  }

  .book-title {
    font-size: 13px;
    -webkit-line-clamp: 2;
    line-clamp: 2;
  }

  .price {
    font-size: 14px;
  }
}
