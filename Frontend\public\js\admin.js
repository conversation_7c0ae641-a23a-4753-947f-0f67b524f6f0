// Admin JavaScript Functions

// User Management Functions
function editUser(userId) {
    // Redirect to edit user page or open modal
    window.location.href = `/admin/user/edit/${userId}`;
}

function deleteUser(userId) {
    if (confirm('Bạn có chắc chắn muốn xóa người dùng này?')) {
        fetch(`/admin/user/delete/${userId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('Xóa người dùng thành công!', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast('Có lỗi xảy ra: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('Có lỗi xảy ra khi xóa người dùng', 'error');
        });
    }
}

// Book Management Functions
function editBook(bookId) {
    window.location.href = `/admin/book/edit/${bookId}`;
}

function deleteBook(bookId) {
    if (confirm('Bạn có chắc chắn muốn xóa sách này?')) {
        fetch(`/admin/book/delete/${bookId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('Xóa sách thành công!', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast('Có lỗi xảy ra: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('Có lỗi xảy ra khi xóa sách', 'error');
        });
    }
}

// Order Management Functions
function viewOrder(orderId) {
    window.location.href = `/admin/order/view/${orderId}`;
}

function updateOrderStatus(orderId, status) {
    fetch(`/admin/order/update-status/${orderId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: status })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Cập nhật trạng thái đơn hàng thành công!');
            location.reload();
        } else {
            alert('Có lỗi xảy ra: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Có lỗi xảy ra khi cập nhật trạng thái');
    });
}

// Category Management Functions
function editCategory(categoryId) {
    window.location.href = `/admin/category/edit/${categoryId}`;
}

function deleteCategory(categoryId) {
    if (confirm('Bạn có chắc chắn muốn xóa danh mục này?')) {
        fetch(`/admin/category/delete/${categoryId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('Xóa danh mục thành công!', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast('Có lỗi xảy ra: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('Có lỗi xảy ra khi xóa danh mục', 'error');
        });
    }
}

// Toast Notification Function
function showToast(message, type = 'success') {
    const toast = document.createElement('div');
    toast.className = `toast-notification ${type}`;

    const iconMap = {
        success: 'fa-check-circle',
        error: 'fa-exclamation-circle',
        warning: 'fa-exclamation-triangle',
        info: 'fa-info-circle'
    };

    const titleMap = {
        success: 'Thành công',
        error: 'Lỗi',
        warning: 'Cảnh báo',
        info: 'Thông tin'
    };

    toast.innerHTML = `
        <div class="toast-header">
            <i class="toast-icon fa-solid ${iconMap[type] || iconMap.success}"></i>
            <div class="toast-title">${titleMap[type] || titleMap.success}</div>
            <button class="toast-close" onclick="this.parentElement.parentElement.remove()">×</button>
        </div>
        <div class="toast-message">${message}</div>
    `;

    document.body.appendChild(toast);

    // Auto remove after 4 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.style.animation = 'slideOutRight 0.3s ease-out';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 300);
        }
    }, 4000);
}

// Form validation
function validateForm(formId) {
    const form = document.getElementById(formId);
    const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
    
    for (let input of inputs) {
        if (!input.value.trim()) {
            alert(`Vui lòng điền ${input.getAttribute('placeholder') || input.name}`);
            input.focus();
            return false;
        }
    }
    return true;
}

// Image preview
function previewImage(input, previewId) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById(previewId).src = e.target.result;
        };
        reader.readAsDataURL(input.files[0]);
    }
}

// Initialize admin functions when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Add any initialization code here
    console.log('Admin panel loaded');
});
