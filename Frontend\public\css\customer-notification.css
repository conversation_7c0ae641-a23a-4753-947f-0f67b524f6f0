/* Customer Notification Styles */
#notifications-container {
    min-height: 200px;
    width: 100%;
}

.empty-state {
    text-align: center;
    padding: 80px 20px;
    color: #666;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 15px;
    color: #dee2e6;
}

.empty-state p {
    margin: 0;
    font-size: 16px;
    color: #6c757d;
}

.notification-item {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.notification-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.notification-item.unread {
    border-left: 4px solid #007bff;
    background: #f8f9ff;
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.notification-title {
    font-weight: 600;
    color: #333;
}

.notification-time {
    font-size: 12px;
    color: #666;
}

.notification-message {
    color: #555;
    line-height: 1.4;
}

/* Legacy notification styles for existing content */
.notify {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-bottom: 20px;
    overflow: hidden;
}

.notify-head {
    display: flex;
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
}

.notify-head img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    margin-right: 15px;
    object-fit: cover;
}

.notify-content {
    flex: 1;
}

.notify-content h3 {
    margin: 0 0 8px 0;
    color: #333;
    font-size: 16px;
}

.notify-content p {
    margin: 0 0 10px 0;
    color: #666;
    line-height: 1.4;
}

.notify-content span {
    color: #007bff;
    font-weight: 500;
}

.notify-content div {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notify-content small {
    color: #999;
    font-size: 12px;
}

.notify-btn {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 5px;
    border-radius: 3px;
    transition: all 0.3s ease;
}

.notify-btn:hover {
    background: #f0f0f0;
    color: #333;
}

.notify-list {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background: #f8f9fa;
}

.notify-list.open {
    max-height: 500px;
    padding: 15px;
}

.step {
    padding: 12px 0;
    border-bottom: 1px solid #e0e0e0;
    position: relative;
    padding-left: 20px;
}

.step:last-child {
    border-bottom: none;
}

.step::before {
    content: '';
    position: absolute;
    left: 0;
    top: 15px;
    width: 8px;
    height: 8px;
    background: #007bff;
    border-radius: 50%;
}

.step h3 {
    margin: 0 0 5px 0;
    color: #333;
    font-size: 14px;
    font-weight: 600;
}

.step p {
    margin: 0 0 5px 0;
    color: #666;
    font-size: 13px;
    line-height: 1.4;
}

.step small {
    color: #999;
    font-size: 11px;
}

.step span {
    color: #007bff;
    font-weight: 500;
}

/* Pagination */
.listpage {
    margin-top: 30px;
    display: flex;
    justify-content: center;
}

.listpage ul {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
    gap: 5px;
}

.listpage li {
    margin: 0;
}

.listpage a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 35px;
    height: 35px;
    border: 1px solid #ddd;
    color: #666;
    text-decoration: none;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.listpage a:hover {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.listpage li.active a {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

/* Loading state */
.notification-loading {
    text-align: center;
    padding: 40px 20px;
    color: #666;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.notification-loading::after {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Container styles */
.customer-right {
    min-height: 400px;
}

.customer-right > div {
    width: 100%;
}

/* Responsive */
@media (max-width: 768px) {
    .notify-head {
        flex-direction: column;
        text-align: center;
    }
    
    .notify-head img {
        margin: 0 auto 10px auto;
    }
    
    .notification-item {
        padding: 12px;
    }
    
    .notification-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}

@media (max-width: 480px) {
    .empty-state {
        padding: 40px 15px;
    }
    
    .empty-state i {
        font-size: 36px;
    }
    
    .notify-list.open {
        padding: 10px;
    }
    
    .step {
        padding-left: 15px;
    }
}
