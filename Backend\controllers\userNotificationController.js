const Notification = require('../models/notificationModel');
const NotificationService = require('../services/notificationService');

// L<PERSON>y danh sách thông báo cho user
const getUserNotifications = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Lấy thông báo của user hiện tại
    const notifications = await Notification.find({
      targetRole: 'user',
      targetUserId: req.user._id
    })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate('data.orderId', 'orderCode totalPrice status')
      .populate('data.userId', 'username');

    const total = await Notification.countDocuments({
      targetRole: 'user',
      targetUserId: req.user._id
    });

    const unreadCount = await Notification.countDocuments({
      targetRole: 'user',
      targetUserId: req.user._id,
      isRead: false
    });

    res.json({
      success: true,
      notifications,
      total,
      unreadCount,
      page,
      pages: Math.ceil(total / limit)
    });
  } catch (error) {
    console.error('Lỗi lấy thông báo user:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server khi lấy thông báo'
    });
  }
};

// Lấy số lượng thông báo chưa đọc
const getUnreadCount = async (req, res) => {
  try {
    const unreadCount = await Notification.countDocuments({
      targetRole: 'user',
      targetUserId: req.user._id,
      isRead: false
    });

    res.json({
      success: true,
      unreadCount
    });
  } catch (error) {
    console.error('Lỗi lấy số thông báo chưa đọc:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server'
    });
  }
};

// Đánh dấu thông báo đã đọc
const markAsRead = async (req, res) => {
  try {
    const { id } = req.params;
    
    const notification = await Notification.findOneAndUpdate(
      { 
        _id: id, 
        targetRole: 'user',
        targetUserId: req.user._id 
      },
      { isRead: true },
      { new: true }
    );

    if (!notification) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy thông báo'
      });
    }

    res.json({
      success: true,
      message: 'Đã đánh dấu đã đọc'
    });
  } catch (error) {
    console.error('Lỗi đánh dấu đã đọc:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server'
    });
  }
};

// Đánh dấu tất cả thông báo đã đọc
const markAllAsRead = async (req, res) => {
  try {
    await Notification.updateMany(
      {
        targetRole: 'user',
        targetUserId: req.user._id,
        isRead: false
      },
      { isRead: true }
    );

    res.json({
      success: true,
      message: 'Đã đánh dấu tất cả đã đọc'
    });
  } catch (error) {
    console.error('Lỗi đánh dấu tất cả đã đọc:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server'
    });
  }
};

module.exports = {
  getUserNotifications,
  getUnreadCount,
  markAsRead,
  markAllAsRead
};
