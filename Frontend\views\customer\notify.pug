doctype html
html
    head
        meta(charset="UTF-8")
        meta(name="viewport", content="width=device-width, initial-scale=1.0")
        title Thông báo - Tài kho<PERSON>n
        link(rel="stylesheet", href="/public/css/main.css")
        link(rel="stylesheet", href="/public/css/grid.css")
        link(rel="stylesheet", href="/public/css/responsive.css")
        link(rel="stylesheet", href="/public/css/customer-notification.css")
        script(src='/public/js/logout.js')
        script(src='/socket.io/socket.io.js')
        link(rel="stylesheet", href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css")
    body
        include ../header.pug
        div(id="customer-header")
            div
                button(class="fa-solid fa-arrow-left" onclick="window.history.back()")
                p <PERSON><PERSON><PERSON> khoản
        div(id="customer")
            div(class="customer-left")
                div
                    div
                        button(class="log-out") Đ<PERSON>ng xuất
                        ul
                            li: a(href="/customer") Thông tin tài khoản
                            li: a(href="/customer/address") Thông tin địa chỉ
                            li: a(href="/customer/change-password") Đổi mật khẩu
                            li: a(href="/customer/orders") Đơn hàng của bạn
                            li(class="active" data-user-id=user._id): a(href="/customer/notifications") Thông báo
            div(class="customer-right")
                div
                    h2(style="margin-bottom: 20px; color: #333;") Thông báo của bạn

                    //- API Notifications Container
                    div(id="notifications-container")
                        .notification-loading Đang tải thông báo...

                    //- Sample notifications with working buttons
                    div(class="notify")
                        div(class="notify-head new")
                            img(src="/public/img/1.jpg", alt="" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIHZpZXdCb3g9IjAgMCA1MCA1MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjUiIGN5PSIyNSIgcj0iMjUiIGZpbGw9IiNlMGUwZTAiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJDMTMuMSAyIDE0IDIuOSAxNCA0VjVIMTBWNEMxMCAyLjkgMTAuOSAyIDEyIDJaTTIxIDlWN0MxOSA3IDE3IDUgMTcgM0gxNUMxNSA1IDEzIDcgMTEgN0g5QzcgNyA1IDUgNSAzSDNDMyA1IDEgNyAxIDlWMTlDMSAyMC4xIDEuOSAyMSAzIDIxSDIxQzIyLjEgMjEgMjMgMjAuMSAyMyAxOVY5SDIxWiIgZmlsbD0iIzk5OTk5OSIvPgo8L3N2Zz4KPC9zdmc+'")
                            div(class="notify-content")
                                h3 Giao hàng thành công
                                p Kiện hàng
                                    span VN258139228241I
                                    |  của đơn hàng
                                    span 250515UD71QSFM
                                    |  đã thành công giao đến bạn. Nếu bạn gặp vấn đề gì, vui lòng liên hệ vào đường dây nóng để được hỗ trợ chi tiết
                                div
                                    small 10:40 17-5-2025
                                    button(class="fa-solid fa-chevron-down notify-btn")
                        div(class="notify-list")
                            div(class="step")
                                h3 Xác nhận đã nhận hàng
                                p Vui lòng nhấn 'Đã nhận được hàng' khi đơn hàng
                                    span 250515UD71QSFM
                                    | đã được giao đến bạn.
                                small 17:11 17-05-2025

                            div(class="step")
                                h3 Đơn hàng đang trên đường giao
                                p Shipper báo rằng: đơn
                                    span 250515UD71QSFM
                                    | đang được giao trong 1–2 ngày tới.
                                small 08:55 17-05-2025

                            div(class="step")
                                h3 Đang vận chuyển
                                p Đơn
                                    span 250515UD71QSFM
                                    | với mã
                                    span VN258139228241I
                                    | đã được vận chuyển.
                                small 14:37 15-05-2025

                            div(class="step")
                                h3 Xác nhận đã thanh toán
                                p Thanh toán thành công. Vui lòng kiểm tra thời gian dự kiến giao hàng.
                                small 00:12 15-05-2025

                    div(class="notify")
                        div(class="notify-head new")
                            img(src="/public/img/1.jpg", alt="" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIHZpZXdCb3g9IjAgMCA1MCA1MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjUiIGN5PSIyNSIgcj0iMjUiIGZpbGw9IiNlMGUwZTAiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJDMTMuMSAyIDE0IDIuOSAxNCA0VjVIMTBWNEMxMCAyLjkgMTAuOSAyIDEyIDJaTTIxIDlWN0MxOSA3IDE3IDUgMTcgM0gxNUMxNSA1IDEzIDcgMTEgN0g5QzcgNyA1IDUgNSAzSDNDMyA1IDEgNyAxIDlWMTlDMSAyMC4xIDEuOSAyMSAzIDIxSDIxQzIyLjEgMjEgMjMgMjAuMSAyMyAxOVY5SDIxWiIgZmlsbD0iIzk5OTk5OSIvPgo8L3N2Zz4KPC9zdmc+'")
                            div(class="notify-content")
                                h3 Đơn hàng mới được xác nhận
                                p Đơn hàng
                                    span AB123456789
                                    |  đã được xác nhận và đang được chuẩn bị. Dự kiến giao hàng trong 2-3 ngày làm việc.
                                div
                                    small 14:30 16-5-2025
                                    button(class="fa-solid fa-chevron-down notify-btn")
                        div(class="notify-list")
                            div(class="step")
                                h3 Đơn hàng được xác nhận
                                p Đơn hàng
                                    span AB123456789
                                    | đã được xác nhận và đang được chuẩn bị.
                                small 14:30 16-05-2025

                            div(class="step")
                                h3 Thanh toán thành công
                                p Thanh toán cho đơn hàng
                                    span AB123456789
                                    | đã được xử lý thành công.
                                small 14:25 16-05-2025

                    div(class="listpage")
                        ul
                            li: a(href=""): i(class="fa-solid fa-chevron-left")
                            li(class="active"): a(href="") 1
                            li: a(href="") 2
                            li: a(href="") 3
                            li: a(href="") ...
                            li: a(href=""): i(class="fa-solid fa-chevron-right")
        include ../footer.pug

        script(src='/public/js/customer-notification.js')
