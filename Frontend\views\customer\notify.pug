doctype html
html
    head
        meta(charset="UTF-8")
        meta(name="viewport", content="width=device-width, initial-scale=1.0")
        title Thông báo - <PERSON><PERSON><PERSON>
        link(rel="stylesheet", href="/public/css/main.css")
        link(rel="stylesheet", href="/public/css/grid.css")
        link(rel="stylesheet", href="/public/css/responsive.css")
        script(src='/public/js/logout.js')
        script(src='/socket.io/socket.io.js')
        link(rel="stylesheet", href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css")
        style.
            .empty-state {
                text-align: center;
                padding: 60px 20px;
                color: #666;
            }
            .empty-state i {
                font-size: 48px;
                margin-bottom: 15px;
                color: #ddd;
            }
            .notification-item {
                background: #fff;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 15px;
                margin-bottom: 15px;
                transition: all 0.3s ease;
            }
            .notification-item:hover {
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }
            .notification-item.unread {
                border-left: 4px solid #007bff;
                background: #f8f9ff;
            }
            .notification-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;
            }
            .notification-title {
                font-weight: 600;
                color: #333;
            }
            .notification-time {
                font-size: 12px;
                color: #666;
            }
            .notification-message {
                color: #555;
                line-height: 1.4;
            }
    body
        include ../header.pug
        div(id="customer-header")
            div
                button(class="fa-solid fa-arrow-left" onclick="window.history.back()")
                p Tài khoản
        div(id="customer")
            div(class="customer-left")
                div
                    div
                        button(class="log-out") Đăng xuất
                        ul
                            li: a(href="/customer") Thông tin tài khoản
                            li: a(href="/customer/address") Thông tin địa chỉ
                            li: a(href="/customer/change-password") Đổi mật khẩu
                            li: a(href="/customer/orders") Đơn hàng của bạn
                            li(class="active" data-user-id=user._id): a(href="/customer/notifications") Thông báo
            div(class="customer-right")
                div
                    h2(style="margin-bottom: 20px; color: #333;") Thông báo của bạn
                    div(id="notifications-container")
                        .empty-state
                            i.fa-solid.fa-bell
                            p Không có thông báo nào
                    //-Không tồn tại đơn nào (dùng code này khi không có đơn hàng nào)
                    //- div(class=pty-order")
                    //-     i(class="fa-solid fa-bell")
                    //-     p Không có thông báo nào

                    div(class="notify") 
                        div(class="notify-head")
                            img(src="./public/img/1.jpg", alt="") 
                            div(class="notify-content") 
                                h3 Giao hàng thành công
                                p Kiện hàng 
                                    span  VN258139228241I 
                                    |  của đơn hàng
                                    span  250515UD71QSFM
                                    |  đã thành công giao đến bạn. Nếu bạn gặp vấn đề gì, vui lòng liên hệ vào đường dây nóng để được hỗ trợ chi tiết
                                div 
                                    small 10:40 17-5-2025
                                    button(class="fa-solid fa-chevron-down notify-btn")
                        div(class="notify-list") 
                            div( class="step") 
                                h3 Xác nhận đã nhận hàng 
                                p Vui lòng nhấn ‘Đã nhận được hàng’ khi đơn hàng 
                                    span 250515UD71QSFM 
                                    | đã được giao đến bạn.
                                small 17:11 17-05-2025
                            
                            div( class="step") 
                                h3 Đơn hàng đang trên đường giao 
                                p Shipper báo rằng: đơn 
                                    span 250515UD71QSFM  
                                    | đang được giao trong 1–2 ngày tới.
                                small 08:55 17-05-2025
                            
                            div( class="step") 
                                h3 Đang vận chuyển 
                                p Đơn 
                                    span 250515UD71QSFM  
                                    | với mã 
                                    span VN258139228241I  
                                    | đã được vận chuyển .
                                small 14:37 15-05-2025
                            
                            div( class="step") 
                                h3 Xác nhận đã thanh toán 
                                p Thanh toán thành công. Vui lòng kiểm tra thời gian dự kiến giao hàng.
                                small 00:12 15-05-2025
                    
                    div(class="notify") 
                        div(class="notify-head")
                            img(src="./public/img/1.jpg", alt="") 
                            div(class="notify-content") 
                                h3 Giao hàng thành công
                                p Kiện hàng 
                                    span  VN258139228241I 
                                    |  của đơn hàng
                                    span  250515UD71QSFM
                                    |  đã thành công giao đến bạn. Nếu bạn gặp vấn đề gì, vui lòng liên hệ vào đường dây nóng để được hỗ trợ chi tiết
                                div 
                                    small 10:40 17-5-2025
                                    button(class="fa-solid fa-chevron-down notify-btn")
                        div(class="notify-list") 
                            div( class="step") 
                                h3 Xác nhận đã nhận hàng 
                                p Vui lòng nhấn ‘Đã nhận được hàng’ khi đơn hàng 
                                    span 250515UD71QSFM 
                                    | đã được giao đến bạn.
                                small 17:11 17-05-2025
                            
                            div( class="step") 
                                h3 Đơn hàng đang trên đường giao 
                                p Shipper báo rằng: đơn 
                                    span 250515UD71QSFM  
                                    | đang được giao trong 1–2 ngày tới.
                                small 08:55 17-05-2025
                            
                            div( class="step") 
                                h3 Đang vận chuyển 
                                p Đơn 
                                    span 250515UD71QSFM  
                                    | với mã 
                                    span VN258139228241I  
                                    | đã được vận chuyển .
                                small 14:37 15-05-2025
                            
                            div( class="step") 
                                h3 Xác nhận đã thanh toán 
                                p Thanh toán thành công. Vui lòng kiểm tra thời gian dự kiến giao hàng.
                                small 00:12 15-05-2025
                    
                    div(class="notify") 
                        div(class="notify-head")
                            img(src="./public/img/1.jpg", alt="") 
                            div(class="notify-content") 
                                h3 Giao hàng thành công
                                p Kiện hàng 
                                    span  VN258139228241I 
                                    |  của đơn hàng
                                    span  250515UD71QSFM
                                    |  đã thành công giao đến bạn. Nếu bạn gặp vấn đề gì, vui lòng liên hệ vào đường dây nóng để được hỗ trợ chi tiết
                                div 
                                    small 10:40 17-5-2025
                                    button(class="fa-solid fa-chevron-down notify-btn")
                        div(class="notify-list") 
                            div( class="step") 
                                h3 Xác nhận đã nhận hàng 
                                p Vui lòng nhấn ‘Đã nhận được hàng’ khi đơn hàng 
                                    span 250515UD71QSFM 
                                    | đã được giao đến bạn.
                                small 17:11 17-05-2025
                            
                            div( class="step") 
                                h3 Đơn hàng đang trên đường giao 
                                p Shipper báo rằng: đơn 
                                    span 250515UD71QSFM  
                                    | đang được giao trong 1–2 ngày tới.
                                small 08:55 17-05-2025
                            
                            div( class="step") 
                                h3 Đang vận chuyển 
                                p Đơn 
                                    span 250515UD71QSFM  
                                    | với mã 
                                    span VN258139228241I  
                                    | đã được vận chuyển .
                                small 14:37 15-05-2025
                            
                            div( class="step") 
                                h3 Xác nhận đã thanh toán 
                                p Thanh toán thành công. Vui lòng kiểm tra thời gian dự kiến giao hàng.
                                small 00:12 15-05-2025
                    
                    div(class="notify") 
                        div(class="notify-head")
                            img(src="./public/img/1.jpg", alt="") 
                            div(class="notify-content") 
                                h3 Giao hàng thành công
                                p Kiện hàng 
                                    span  VN258139228241I 
                                    |  của đơn hàng
                                    span  250515UD71QSFM
                                    |  đã thành công giao đến bạn. Nếu bạn gặp vấn đề gì, vui lòng liên hệ vào đường dây nóng để được hỗ trợ chi tiết
                                div 
                                    small 10:40 17-5-2025
                                    button(class="fa-solid fa-chevron-down notify-btn")
                        div(class="notify-list") 
                            div( class="step") 
                                h3 Xác nhận đã nhận hàng 
                                p Vui lòng nhấn ‘Đã nhận được hàng’ khi đơn hàng 
                                    span 250515UD71QSFM 
                                    | đã được giao đến bạn.
                                small 17:11 17-05-2025
                            
                            div( class="step") 
                                h3 Đơn hàng đang trên đường giao 
                                p Shipper báo rằng: đơn 
                                    span 250515UD71QSFM  
                                    | đang được giao trong 1–2 ngày tới.
                                small 08:55 17-05-2025
                            
                            div( class="step") 
                                h3 Đang vận chuyển 
                                p Đơn 
                                    span 250515UD71QSFM  
                                    | với mã 
                                    span VN258139228241I  
                                    | đã được vận chuyển .
                                small 14:37 15-05-2025
                            
                            div( class="step") 
                                h3 Xác nhận đã thanh toán 
                                p Thanh toán thành công. Vui lòng kiểm tra thời gian dự kiến giao hàng.
                                small 00:12 15-05-2025
    
                    div(class="listpage")
                        ul 
                            li: a(href=""): i(class="fa-solid fa-chevron-left")         
                            li(class="active"): a(href="") 1        
                            li: a(href="") 2        
                            li: a(href="") 3        
                            li: a(href="") ...        
                            li: a(href=""): i(class="fa-solid fa-chevron-right")
        include ../footer.pug

        script.
            // Customer Notification Manager
            class CustomerNotificationManager {
                constructor() {
                    this.init();
                }

                init() {
                    this.loadNotifications();
                    this.setupSocketConnection();
                }

                async loadNotifications() {
                    try {
                        const response = await fetch('/api/user-notifications');
                        if (response.ok) {
                            const data = await response.json();
                            if (data.success) {
                                this.renderNotifications(data.notifications || []);
                            }
                        } else {
                            console.log('API chưa sẵn sàng, hiển thị trạng thái trống');
                        }
                    } catch (error) {
                        console.log('Lỗi tải thông báo:', error);
                    }
                }

                renderNotifications(notifications) {
                    const container = document.getElementById('notifications-container');

                    if (!notifications || notifications.length === 0) {
                        container.innerHTML = `
                            <div class="empty-state">
                                <i class="fa-solid fa-bell"></i>
                                <p>Không có thông báo nào</p>
                            </div>
                        `;
                        return;
                    }

                    container.innerHTML = notifications.map(notification => `
                        <div class="notification-item ${!notification.isRead ? 'unread' : ''}"
                             onclick="customerNotificationManager.handleNotificationClick('${notification._id}', '${notification.type}', '${notification.data?.orderId || ''}')">
                            <div class="notification-header">
                                <div class="notification-title">${notification.title}</div>
                                <div class="notification-time">${this.formatTime(notification.createdAt)}</div>
                            </div>
                            <div class="notification-message">${notification.message}</div>
                        </div>
                    `).join('');
                }

                formatTime(dateString) {
                    const date = new Date(dateString);
                    const now = new Date();
                    const diffInMinutes = Math.floor((now - date) / (1000 * 60));

                    if (diffInMinutes < 1) return 'Vừa xong';
                    if (diffInMinutes < 60) return `${diffInMinutes} phút trước`;
                    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)} giờ trước`;
                    return `${Math.floor(diffInMinutes / 1440)} ngày trước`;
                }

                async handleNotificationClick(notificationId, type, orderId) {
                    // Mark as read
                    await this.markAsRead(notificationId);

                    // Handle different notification types
                    switch(type) {
                        case 'order_confirmation':
                        case 'order_status_update':
                            window.location.href = '/customer/orders';
                            break;
                        default:
                            console.log('Clicked notification:', notificationId);
                    }
                }

                async markAsRead(notificationId) {
                    try {
                        await fetch(`/api/user-notifications/${notificationId}/read`, {
                            method: 'PUT'
                        });
                        // Reload notifications to update UI
                        this.loadNotifications();
                    } catch (error) {
                        console.error('Lỗi đánh dấu đã đọc:', error);
                    }
                }

                setupSocketConnection() {
                    if (typeof io !== 'undefined') {
                        const socket = io();
                        const userId = document.querySelector('[data-user-id]')?.getAttribute('data-user-id');

                        if (userId) {
                            socket.emit('join-user', userId);

                            socket.on('new-notification', (notification) => {
                                this.loadNotifications(); // Reload to show new notification
                            });
                        }
                    }
                }
            }

            // Initialize when page loads
            const customerNotificationManager = new CustomerNotificationManager();
    script.
        const notifyBtn = document.querySelector(".notify-btn")
        const notifyList = document.querySelector(".notify-list")
        //mở form Change-addr
        notifyBtn.addEventListener("click", function () {
            // Toggle class hiển thị
            const isOpen = notifyList.classList.toggle("open");

            // Đổi icon mũi tên
            if (isOpen) {
                this.classList.ove("fa-chevron-down");
                this.classList.add("fa-chevron-up");
            } else {
                this.classList.ove("fa-chevron-up");
                this.classList.add("fa-chevron-down");
            }
        });
        