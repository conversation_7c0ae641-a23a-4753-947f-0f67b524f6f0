// Customer Notification Manager
class CustomerNotificationManager {
    constructor() {
        this.init();
    }

    init() {
        this.loadNotifications();
        this.setupSocketConnection();
        this.setupLegacyNotifications();
    }

    async loadNotifications() {
        try {
            const response = await fetch('/api/user-notifications');
            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.renderNotifications(data.notifications || []);
                } else {
                    this.renderEmptyState();
                }
            } else {
                // API không sẵn sàng, hiển thị empty state
                this.renderEmptyState();
            }
        } catch (error) {
            console.log('Lỗi tải thông báo:', error);
            this.renderEmptyState();
        }
    }

    renderNotifications(notifications) {
        const container = document.getElementById('notifications-container');

        if (!container) return;

        if (!notifications || notifications.length === 0) {
            this.renderEmptyState();
            return;
        }

        container.innerHTML = notifications.map(notification => `
            <div class="notification-item ${!notification.isRead ? 'unread' : ''}"
                 onclick="customerNotificationManager.handleNotificationClick('${notification._id}', '${notification.type}', '${notification.data?.orderId || ''}')">
                <div class="notification-header">
                    <div class="notification-title">${notification.title}</div>
                    <div class="notification-time">${this.formatTime(notification.createdAt)}</div>
                </div>
                <div class="notification-message">${notification.message}</div>
            </div>
        `).join('');
    }

    renderEmptyState() {
        const container = document.getElementById('notifications-container');
        if (container) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fa-solid fa-bell"></i>
                    <p>Không có thông báo nào</p>
                </div>
            `;
        }
    }

    formatTime(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffInMinutes = Math.floor((now - date) / (1000 * 60));
        
        if (diffInMinutes < 1) return 'Vừa xong';
        if (diffInMinutes < 60) return `${diffInMinutes} phút trước`;
        if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)} giờ trước`;
        return `${Math.floor(diffInMinutes / 1440)} ngày trước`;
    }

    async handleNotificationClick(notificationId, type, orderId) {
        // Mark as read
        await this.markAsRead(notificationId);
        
        // Handle different notification types
        switch(type) {
            case 'order_confirmation':
            case 'order_status_update':
                window.location.href = '/customer/orders';
                break;
            default:
                console.log('Clicked notification:', notificationId);
        }
    }

    async markAsRead(notificationId) {
        try {
            await fetch(`/api/user-notifications/${notificationId}/read`, {
                method: 'PUT'
            });
            // Reload notifications to update UI
            this.loadNotifications();
        } catch (error) {
            console.error('Lỗi đánh dấu đã đọc:', error);
        }
    }

    setupSocketConnection() {
        if (typeof io !== 'undefined') {
            const socket = io();
            const userId = document.querySelector('[data-user-id]')?.getAttribute('data-user-id');
            
            if (userId) {
                socket.emit('join-user', userId);
                
                socket.on('new-notification', (notification) => {
                    this.loadNotifications(); // Reload to show new notification
                });
            }
        }
    }

    // Setup legacy notification expand/collapse functionality
    setupLegacyNotifications() {
        // Handle multiple notify buttons
        const notifyBtns = document.querySelectorAll(".notify-btn");
        
        notifyBtns.forEach(btn => {
            btn.addEventListener("click", function () {
                // Find the corresponding notify-list for this button
                const notifyContainer = this.closest('.notify');
                const notifyList = notifyContainer?.querySelector('.notify-list');
                
                if (notifyList) {
                    // Toggle class hiển thị
                    const isOpen = notifyList.classList.toggle("open");

                    // Đổi icon mũi tên
                    if (isOpen) {
                        this.classList.remove("fa-chevron-down");
                        this.classList.add("fa-chevron-up");
                    } else {
                        this.classList.remove("fa-chevron-up");
                        this.classList.add("fa-chevron-down");
                    }
                }
            });
        });
    }

    // Method to show toast notifications
    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <i class="fa-solid ${type === 'success' ? 'fa-check-circle' : 'fa-info-circle'}"></i>
            <span>${message}</span>
        `;
        
        // Add styles
        Object.assign(toast.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: type === 'success' ? '#d4edda' : '#d1ecf1',
            color: type === 'success' ? '#155724' : '#0c5460',
            border: `1px solid ${type === 'success' ? '#c3e6cb' : '#bee5eb'}`,
            borderRadius: '8px',
            padding: '12px 16px',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            zIndex: '10000',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease'
        });
        
        document.body.appendChild(toast);
        
        // Show toast
        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
        }, 100);
        
        // Remove toast after 3 seconds
        setTimeout(() => {
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (document.body.contains(toast)) {
                    document.body.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }

    // Method to refresh notifications
    refresh() {
        this.loadNotifications();
    }
}

// Initialize when page loads
let customerNotificationManager;
document.addEventListener('DOMContentLoaded', () => {
    customerNotificationManager = new CustomerNotificationManager();
});

// Export for global access
window.customerNotificationManager = customerNotificationManager;
