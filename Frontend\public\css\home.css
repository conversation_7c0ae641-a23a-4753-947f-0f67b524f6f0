@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');

*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}
header{
    font-family: 'Roboto', sans-serif;
}
.header{
    width: 100%;
    height: auto;
    text-align: center;
    background-color:#c12530 ;
}
.menu{
    padding:20px;
    height: 60px;
    background-color: white;
    display: flex;
    align-items: center;
}
.menu ul{
    display: flex;
    justify-content: space-around;
    align-items: center;
    width: 100%;
    height: 100%;
}
.menu ul li a:hover{
    background:linear-gradient(to right, #e03a3a, #ec7e7e);
    color: white;
    transition: 0.8s;
    border-radius: 15px;
}


/* Notification Styles */
.notification-container {
    position: relative;
    display: inline-block;
}

.notification-container a {
    position: relative;
    text-decoration: none;
}

.notification-container a:hover {
    background: linear-gradient(to right, #e03a3a, #ec7e7e);
    color: white;
    transition: 0.8s;
    border-radius: 15px;
}

.notification-container a:hover i {
    color: white;
}

/* Đảm bảo tất cả nút menu có cùng hiệu ứng hover */
.menu ul li a:hover {
    background: linear-gradient(to right, #e03a3a, #ec7e7e);
    color: white;
    transition: 0.8s;
    border-radius: 15px;
}

.menu ul li a:hover i {
    color: white;
}

/* Đảm bảo tất cả các li trong menu có cùng styling */
.menu ul li {
    list-style-type: none;
    display: flex;
    align-items: center;
    height: 60px;
}

.menu ul li a {
    text-align: center;
    color: black;
    padding: 21px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 60px;
}

/* Đảm bảo icon có cùng kích thước */
.menu ul li a i {
    font-size: 18px;
    color: black;
}

.notification-container {
    display: flex;
    align-items: center;
    height: 60px;
}

.notification-container a {
    text-align: center;
    color: black;
    padding: 21px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 60px;
}

.notification-container a i {
    font-size: 18px;
    color: black;
}

.notification-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: #dc3545;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 12px;
    font-weight: bold;
    min-width: 18px;
    text-align: center;
    line-height: 1.2;
}

.notification-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
    width: 350px;
    max-height: 400px;
    z-index: 1000;
    overflow: hidden;
}

.notification-header {
    padding: 15px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f8f9fa;
}

.notification-header h4 {
    margin: 0;
    color: #333;
    font-size: 16px;
}

.mark-all-read {
    background: none;
    border: none;
    color: #007bff;
    cursor: pointer;
    font-size: 12px;
    padding: 5px 10px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.mark-all-read:hover {
    background-color: #e9ecef;
}

.notification-list {
    max-height: 300px;
    overflow-y: auto;
}

.notification-item {
    padding: 12px 15px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.2s;
}

.notification-item {
    cursor: pointer;
}

.notification-item:hover {
    background-color: #f8f9fa;
    transform: translateX(2px);
    transition: all 0.2s ease;
}

.notification-item.unread {
    background-color: #fff3cd;
    border-left: 3px solid #ffc107;
}

.notification-title {
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notification-message {
    color: #666;
    font-size: 14px;
    margin-bottom: 5px;
    line-height: 1.4;
}

.notification-time {
    color: #999;
    font-size: 12px;
}

.notification-priority {
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    color: white;
    font-weight: bold;
}

.priority-low { background-color: #28a745; }
.priority-medium { background-color: #ffc107; }
.priority-high { background-color: #fd7e14; }
.priority-urgent { background-color: #dc3545; }

.notification-empty {
    padding: 20px;
    text-align: center;
    color: #999;
    font-style: italic;
}

.notification-loading {
    padding: 20px;
    text-align: center;
    color: #666;
}

.notification-footer {
    padding: 10px 15px;
    border-top: 1px solid #eee;
    text-align: center;
    background-color: #f8f9fa;
}

.notification-footer a {
    color: #007bff;
    text-decoration: none;
    font-size: 14px;
    padding: 5px 10px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.notification-footer a:hover {
    background-color: #e9ecef;
}

/* Toast Notification Styles for Success Messages */
.toast-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    z-index: 9999;
    min-width: 300px;
    max-width: 400px;
    animation: slideInRight 0.3s ease-out;
    border-left: 4px solid #28a745;
}

.toast-notification.success {
    border-left-color: #28a745;
}

.toast-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    border-bottom: 1px solid #eee;
    background-color: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.toast-body {
    padding: 12px 15px;
    color: #333;
    line-height: 1.4;
}

.toast-close {
    background: none;
    border: none;
    font-size: 18px;
    font-weight: bold;
    color: #999;
    cursor: pointer;
    padding: 0;
    margin-left: 10px;
}

.toast-close:hover {
    color: #333;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.toast-notification.error {
    border-left-color: #dc3545;
}

.toast-notification.warning {
    border-left-color: #ffc107;
}

.toast-notification.info {
    border-left-color: #17a2b8;
}

.toast-header {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    border-bottom: 1px solid #eee;
    background-color: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.toast-icon {
    margin-right: 10px;
    font-size: 16px;
}

.toast-notification.success .toast-icon {
    color: #28a745;
}

.toast-notification.error .toast-icon {
    color: #dc3545;
}

.toast-notification.warning .toast-icon {
    color: #ffc107;
}

.toast-notification.info .toast-icon {
    color: #17a2b8;
}

.toast-title {
    font-weight: bold;
    color: #333;
    flex: 1;
}

.toast-close {
    background: none;
    border: none;
    font-size: 18px;
    color: #999;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.toast-close:hover {
    color: #666;
}

.toast-message {
    padding: 12px 15px;
    color: #666;
    line-height: 1.4;
}

/* Alert Styles for Page Messages */
.alert {
    padding: 12px 15px;
    margin: 15px 0;
    border: 1px solid transparent;
    border-radius: 6px;
    display: flex;
    align-items: center;
    font-size: 14px;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-error {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

.alert i {
    margin-right: 8px;
    font-size: 16px;
}

/* Animation for toast notifications */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Success message specific styles */
.success-message {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    margin: 15px 0;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    display: flex;
    align-items: center;
    animation: fadeInUp 0.5s ease-out;
}

.success-message i {
    margin-right: 10px;
    font-size: 18px;
}

@keyframes fadeInUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.menu ul li input{
    width: 555px;
    height: 40px;
    border-radius: 5px;
    font-size: 15px;
    padding-left: 20px;
    border: 1px solid #ddd;
    box-sizing: border-box;
}

.menu ul li form {
    display: flex;
    align-items: center;
    height: 60px;
    position: relative;
}
.menu ul li:hover {
    color: black;
}
.menu ul li .fa-magnifying-glass {
    background-color: #e04852;
    width: 70px;
    height: 40px;
    padding: 0;
    color: white;
    font-size: 20px;
    margin-left: -70px;
    border-radius: 0 5px 5px 0;
    text-align: center;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.menu .sub-menu{
    display:none ;
    position: absolute;
    flex-direction: column;
    top: 124px;
    background-color: white;
    box-shadow: 0 0 10px darkgrey;
    border-radius: 8px;
    z-index: 1;
}

.menu .sub-menu li {
    list-style-type: none;
    padding:  20px;
    text-align: center;
    font-size: 14px;
    color: black;

}
.menu .sub-menu li a{
    text-decoration: none;
    color: black;
    padding: 10px 30px;
}
.menu li:hover .sub-menu  {
    display: flex;

}


.anh {
    width: 1220px;
    overflow: hidden;
    margin: 20px auto;
    position: relative;
}

.anh2 {
    display: flex;
    width: calc(1220px * 3); /* số ảnh * chiều rộng ảnh */
    transition: transform 0.5s ease-in-out;

}

.anh2 img {
    width: 1220px;
    border-radius: 20px;
}
.prev-btn,
.next-btn {
    position: absolute;
    top: 50%;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    padding: 10px;
    cursor: pointer;
    font-size: 24px;


}

.prev-btn {
    left: 20px;
}

.next-btn {
    right: 20px;
}

.prev-btn:hover,
.next-btn:hover {
    background-color: rgba(0, 0, 0, 0.8);
}




/*** silder ***/




.silder{
    font-family: 'Roboto', sans-serif;
}
 h1{
    text-align: center;
    font-size: 30px;
    padding: 10px;


}
.banchay{
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 15px;
    background: linear-gradient(to right, #c03d46, #f0f0f0);
    border-radius: 8px;
    margin-bottom: 50px;
    padding: 20px;
    align-items: start;
    justify-items: center;
}
.banchay h1{
    display: none;
}
.banchay .banchay1{
    width: 100%;
    text-align: center;
    padding: 15px 10px;
    max-width: 200px;
    margin: 0 auto;
}

.banchay .banchay1 a {
    text-decoration: none;
    color: inherit;
    display: block;
}

.banchay .banchay1 img{
    width: 100%;
    max-width: 150px;
    height: 200px;
    object-fit: cover;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.banchay .banchay1 img:hover {
    transform: scale(1.05);
}

.banchay .banchay1 h3 {
    font-size: 14px;
    margin: 8px 0 4px 0;
    color: #333;
    font-weight: 600;
}

.banchay .banchay1 p {
    font-size: 13px;
    color: #666;
    margin: 0;
    line-height: 1.3;
    height: 35px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

h2{
    position: relative;
    background:linear-gradient(to right, #f9eaea, #db8b8b);
    color: black;
    padding:20px 0 ;
    border-radius: 8px;
    text-align: left;
    padding-left: 50px;
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 20px;
}
h2 i{
    display: none;
}

.slider2, .slider3 {
    margin-bottom: 40px;
}

.slider3 h2 {
    background: linear-gradient(to right, #ff6b6b, #ffa8a8);
}

.slider2 h2 {
    background: linear-gradient(to right, #ff6b6b, #ffa8a8);
}

a{
    text-decoration: none;
}









/***** footer *****/
footer{
    width: 100%;
    height: 300px;
    background:linear-gradient(to top, #303030, #676565);
    color: white;
    border-radius: 8px;
}
.footer{
    display: flex;
    justify-content: space-around;
    align-items: center;
}



.footer .chantrang h3{
    font-size: 30px;
    padding: 30px 0;
    text-align: center;
}



.footer .chantrang ul li a{
    text-decoration: none;
    color: white;
    list-style-type: none;
    font-size: 18px;
    padding: 14px 0;

}
.footer .chantrang ul li a:hover,
 .footer .chantrang:hover h3{
    color: gold;
}



.xuhuong{
    width: 100%;
    height: 100%;

}
.xuhuong_hot{

    display: flex;
    align-items: center;
    height: 40px;
    box-shadow: 0 0 5px rgba(0,0,0,0.5);

}
.xuhuong_hot p{
    font-size: 18px;
    padding: 0 20px;
}
.xuhuong_hot p:hover{
    color: red;
    border-bottom: 2px solid red;
}

/* Responsive Design */
@media (max-width: 768px) {
    .banchay {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 10px;
        padding: 15px;
    }

    .banchay .banchay1 {
        max-width: 150px;
        padding: 10px 5px;
    }

    .banchay .banchay1 img {
        max-width: 120px;
        height: 160px;
    }

    .banchay .banchay1 h3 {
        font-size: 12px;
    }

    .banchay .banchay1 p {
        font-size: 11px;
        height: 30px;
    }
}

@media (max-width: 480px) {
    .banchay {
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
        padding: 10px;
    }

    .banchay .banchay1 {
        max-width: 130px;
    }

    .banchay .banchay1 img {
        max-width: 100px;
        height: 140px;
    }
}
