doctype html
html(lang='vi')
  head
    meta(charset='UTF-8')
    meta(name='viewport' content='width=device-width, initial-scale=1.0')
    title ADMIN - Quản lí đơn hàng
    link(rel='stylesheet' href='/public/css/admin.css')
    link(rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css')
  body
    header
      .content
        ul.logo
          li
            h3 WEB BÁN SÁCH TRUYỆN NHÓM 9 - ADMIN
          li
            a(href='/auth/logout')
              | Đăng xuất
              i.fa-solid.fa-right-from-bracket
        ul.quanli
          li
            a(href='/admin')
              i.fa-solid.fa-house
              h4 Quản lí thông tin tổng quát
          li
            a(href='/admin/user')
              i.fa-solid.fa-circle-user
              h4 Quản lí người dùng
          li
            a(href='/admin/book')
              i.fa-solid.fa-book
              h4 Quản lí sách
          li
            a(href='/admin/category')
              i.fa-solid.fa-address-book
              h4 Quản lí danh mục
          li
            a(href='/admin/order')
              i.fa-solid.fa-cart-shopping
              h4 Quản lí đơn hàng
          li
            a(href='/admin/sale')
              i.fa-solid.fa-sack-dollar
              h4 Quản lí doanh thu
          li
            a(href='/')
              i.fa-solid.fa-share-from-square
              h4 Quay về trang chủ
    .main
      .admin
        h2 Quản lí đơn hàng
        // Hiển thị thông báo thành công hoặc lỗi
        if success
          .alert.alert-success(style="background-color: #d4edda; color: #155724; padding: 10px; margin: 10px 0; border: 1px solid #c3e6cb; border-radius: 4px;")
            i.fa-solid.fa-check-circle(style="margin-right: 8px;")
            = success
        if error
          .alert.alert-error(style="background-color: #f8d7da; color: #721c24; padding: 10px; margin: 10px 0; border: 1px solid #f5c6cb; border-radius: 4px;")
            i.fa-solid.fa-exclamation-triangle(style="margin-right: 8px;")
            = error
    .donhang
      .danhsachdonhang
        h3 QUẢN LÍ ĐƠN HÀNG
        table.thongtindonhang
          tr
            th STT
            th Mã đơn
            th Khách hàng
            th ngày đặt
            th trạng thái
            th Thao tác
          if orders && orders.length > 0
            each order, index in orders
              tr(class=highlightOrderId && highlightOrderId === order._id.toString() ? 'highlight-order' : '', id=`order-${order._id}`)
                td= index + 1
                td= order._id
                td= order.user ? order.user.username : 'Không có thông tin'
                td= new Date(order.createdAt).toLocaleDateString('vi-VN')
                td
                  span(class=`status-${order.status}`)
                    if order.status === 'pending'
                      | Chờ xác nhận
                    else if order.status === 'transporting'
                      | Đang giao hàng
                    else if order.status === 'delivered'
                      | Đã giao thành công
                    else if order.status === 'cancelled'
                      | Đã hủy
                    else
                      = order.status
                td
                  button.btn2(onclick=`viewOrder('${order._id}')`)
                    i.fa-solid.fa-eye
          else
            tr
              td(colspan="6" style="text-align: center; padding: 20px;") Không có dữ liệu đơn hàng
    script(src='/public/js/admin.js')
    script.
      // Scroll to highlighted order if exists
      document.addEventListener('DOMContentLoaded', function() {
        const urlParams = new URLSearchParams(window.location.search);
        const orderId = urlParams.get('orderId');

        if (orderId) {
          const orderElement = document.getElementById(`order-${orderId}`);
          if (orderElement) {
            // Scroll to the order with smooth animation
            setTimeout(() => {
              orderElement.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
              });

              // Show a toast notification
              if (typeof showToast === 'function') {
                showToast('Đã tìm thấy đơn hàng từ thông báo!', 'info');
              }
            }, 500);
          }
        }
      });
