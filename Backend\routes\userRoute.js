const express = require('express');
const router = express.Router();
const { auth } = require('../middleware/authMiddleware');
const { getUserProfile, updateUser, updateAddress } = require('../controllers/userController');
const { getMyOrders, cancelOrder } = require('../controllers/orderController');
const { getReviewById } = require('../controllers/reviewController');
const Category = require('../models/categoryModel');

router.get('/customer', auth, async (req, res) => {
  const user = await getUserProfile(req, res, true);
  const categories = await Category.find().sort({ name: 1 });
  if (user) {
    res.render('customer/customer', { user, categories });
  }
});

router.get('/customer/address', auth, async (req, res) => {
  const user = await getUserProfile(req, res, true);
  const categories = await Category.find().sort({ name: 1 });
  if (user) {
    res.render('customer/customer-addr', { user, categories });
  }
});

router.get('/customer/change-password', auth, async (req, res) => {
  const categories = await Category.find().sort({ name: 1 });
  res.render('customer/change-key', { categories });
});

router.get('/customer/notifications', auth, async (req, res) => {
  const user = await getUserProfile(req, res, true);
  const categories = await Category.find().sort({ name: 1 });
  if (user) {
    res.render('customer/notify', { user, categories });
  }
});

router.get('/customer/orders', auth, getMyOrders);

router.get('/customer/orders/check', auth, (req, res) => {
  req.query.status = 'pending';
  getMyOrders(req, res);
});
router.get('/customer/orders/transport', auth, (req, res) => {
  req.query.status = 'transporting';
  getMyOrders(req, res);
});
router.get('/customer/orders/receive', auth, (req, res) => {
  req.query.status = 'delivered';
  getMyOrders(req, res);
});
router.get('/customer/orders/cancel', auth, (req, res) => {
  req.query.status = 'cancelled';
  getMyOrders(req, res);
});

router.post('/customer/update', auth, async (req, res) => {
  const success = await updateUser(req, res);
  if (success) {
    res.redirect('/customer');
  } else {
    res.status(500).send('Lỗi cập nhật');
  }
});

router.post('/customer/address/update', auth, updateAddress);

// Route hủy đơn hàng
router.post('/customer/orders/:orderId/cancel', auth, cancelOrder);

// Route xem đánh giá
router.get('/reviews/:reviewId', auth, getReviewById);

module.exports = router;