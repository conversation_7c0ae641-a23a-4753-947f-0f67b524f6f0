
header
    div(class="container header-container")
        div(class="logo")
            a(href="/")
                h2 BOOKSTORE
        ul(class="menu")
            li
                i(class="fa-solid fa-bars")
                ul(class="menu-genre")
                    if categories && categories.length > 0
                        each category in categories
                            li: a(href=`/books/category/${category._id}`)= category.name
                    else
                        li: span(style="color: #999;") Chưa có danh mục
        div(class="header-search")
            form(action="/search" method="GET" id="search-form")
                div.search-input-container
                    input(type="text", placeholder="Tìm kiếm sản phẩm", name="q", id="search-input", autocomplete="off")
                    button(type="submit" name="search" class="submit-product")
                        i.fa-solid.fa-magnifying-glass
                    div.search-suggestions(id="search-suggestions")
        div(class="card__head")
            div(class="notify")
                a(href="#" id="header-notification-bell")
                    div(class="notify-bell")
                        i(class="fa-solid fa-bell")
                        span.notification-badge(id="header-notification-badge" style="display: none;") 0
                        p Thông báo
                ul(class="notify-content" id="header-notification-dropdown")
                    li(class="notification-loading") Đang tải thông báo...
                    li: a(href="/customer/notifications") Xem tất cả thông báo
            a(href="/customer")
                i(class="fa-solid fa-user")
                p Tài khoản
            a(href="/cart" class="card")
                i(class="fa-solid fa-cart-shopping")
                p Giỏ hàng

            if user && user.role === 'admin'
                a(href="/admin" class="admin-link")
                    i(class="fa-solid fa-cog")
                    p Admin

script(src='/public/js/search.js')
link(rel="stylesheet", href="/public/css/header-notification.css")
script(src='/public/js/header-notification.js')