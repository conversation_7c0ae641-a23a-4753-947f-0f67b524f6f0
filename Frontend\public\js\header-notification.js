// Header Notification Manager
class HeaderNotificationManager {
    constructor() {
        this.notificationBell = document.getElementById('header-notification-bell');
        this.notificationBadge = document.getElementById('header-notification-badge');
        this.notificationDropdown = document.getElementById('header-notification-dropdown');
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadNotifications();
        this.setupSocketConnection();
    }

    setupEventListeners() {
        // Toggle dropdown
        if (this.notificationBell) {
            this.notificationBell.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.toggleDropdown();
            });
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.notify')) {
                this.closeDropdown();
            }
        });
    }

    toggleDropdown() {
        if (this.notificationDropdown) {
            this.notificationDropdown.classList.toggle('show');
            if (this.notificationDropdown.classList.contains('show')) {
                this.loadNotifications();
            }
        }
    }

    closeDropdown() {
        if (this.notificationDropdown) {
            this.notificationDropdown.classList.remove('show');
        }
    }

    async loadNotifications() {
        try {
            const response = await fetch('/api/user-notifications');
            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.renderNotifications(data.notifications || []);
                    this.updateBadge(data.unreadCount || 0);
                }
            } else {
                this.renderError('Không thể tải thông báo');
            }
        } catch (error) {
            console.log('Lỗi tải thông báo:', error);
            this.renderError('Lỗi kết nối');
        }
    }

    renderNotifications(notifications) {
        if (!this.notificationDropdown) return;

        // Keep the "Xem tất cả" link
        const viewAllLink = this.notificationDropdown.querySelector('li:last-child');
        
        if (notifications.length === 0) {
            this.notificationDropdown.innerHTML = `
                <li class="notification-empty">Không có thông báo mới</li>
                ${viewAllLink ? viewAllLink.outerHTML : '<li><a href="/customer/notifications">Xem tất cả thông báo</a></li>'}
            `;
            return;
        }

        // Show latest 5 notifications
        const latestNotifications = notifications.slice(0, 5);
        
        const notificationItems = latestNotifications.map(notification => `
            <li class="${!notification.isRead ? 'unread' : ''}" data-id="${notification._id}">
                <a href="#" onclick="headerNotificationManager.handleNotificationClick('${notification._id}', '${notification.type}', '${notification.data?.orderId || ''}')">
                    <img src="/public/img/notification-icon.png" alt="" onerror="this.src='/public/img/1.jpg'">
                    <div>
                        <div class="notification-item-header">
                            <div class="notification-title">${notification.title}</div>
                            <div class="notification-time">${this.formatTime(notification.createdAt)}</div>
                        </div>
                        <div class="notification-message">${notification.message}</div>
                    </div>
                </a>
            </li>
        `).join('');

        this.notificationDropdown.innerHTML = `
            ${notificationItems}
            ${viewAllLink ? viewAllLink.outerHTML : '<li><a href="/customer/notifications">Xem tất cả thông báo</a></li>'}
        `;
    }

    renderError(message) {
        if (this.notificationDropdown) {
            this.notificationDropdown.innerHTML = `
                <li class="notification-empty">${message}</li>
                <li><a href="/customer/notifications">Xem tất cả thông báo</a></li>
            `;
        }
    }

    updateBadge(count) {
        if (this.notificationBadge) {
            if (count > 0) {
                this.notificationBadge.textContent = count > 99 ? '99+' : count;
                this.notificationBadge.style.display = 'flex';
            } else {
                this.notificationBadge.style.display = 'none';
            }
        }
    }

    formatTime(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffInMinutes = Math.floor((now - date) / (1000 * 60));
        
        if (diffInMinutes < 1) return 'Vừa xong';
        if (diffInMinutes < 60) return `${diffInMinutes}p`;
        if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h`;
        return `${Math.floor(diffInMinutes / 1440)}d`;
    }

    async handleNotificationClick(notificationId, type, orderId) {
        // Mark as read first
        await this.markAsRead(notificationId);
        
        // Close dropdown
        this.closeDropdown();
        
        // Handle different notification types
        switch(type) {
            case 'order_confirmation':
            case 'order_status_update':
                if (orderId) {
                    window.location.href = `/customer/orders`;
                } else {
                    window.location.href = '/customer/orders';
                }
                break;
            default:
                window.location.href = '/customer/notifications';
        }
    }

    async markAsRead(notificationId) {
        try {
            const response = await fetch(`/api/user-notifications/${notificationId}/read`, {
                method: 'PUT'
            });

            if (response.ok) {
                // Update UI
                const item = document.querySelector(`[data-id="${notificationId}"]`);
                if (item) {
                    item.classList.remove('unread');
                }
                this.updateUnreadCount();
            }
        } catch (error) {
            console.error('Lỗi đánh dấu đã đọc:', error);
        }
    }

    async updateUnreadCount() {
        try {
            const response = await fetch('/api/user-notifications/unread-count');
            if (response.ok) {
                const data = await response.json();
                this.updateBadge(data.count || 0);
            }
        } catch (error) {
            console.error('Lỗi cập nhật số thông báo:', error);
        }
    }

    setupSocketConnection() {
        if (typeof io !== 'undefined') {
            const socket = io();
            const userId = document.querySelector('[data-user-id]')?.getAttribute('data-user-id');
            
            if (userId) {
                socket.emit('join-user', userId);
                
                socket.on('new-notification', (notification) => {
                    this.loadNotifications();
                    this.showToast(`Thông báo mới: ${notification.title}`, 'info');
                });
            }
        }
    }

    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <i class="fa-solid ${type === 'info' ? 'fa-info-circle' : 'fa-bell'}"></i>
            <span>${message}</span>
        `;
        
        // Add styles
        Object.assign(toast.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: type === 'info' ? '#d1ecf1' : '#d4edda',
            color: type === 'info' ? '#0c5460' : '#155724',
            border: `1px solid ${type === 'info' ? '#bee5eb' : '#c3e6cb'}`,
            borderRadius: '8px',
            padding: '12px 16px',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            zIndex: '10000',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease'
        });
        
        document.body.appendChild(toast);
        
        // Show toast
        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
        }, 100);
        
        // Remove toast after 3 seconds
        setTimeout(() => {
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (document.body.contains(toast)) {
                    document.body.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }

    // Method to refresh notifications
    refresh() {
        this.loadNotifications();
    }
}

// Initialize when page loads
let headerNotificationManager;
document.addEventListener('DOMContentLoaded', () => {
    headerNotificationManager = new HeaderNotificationManager();
});

// Export for global access
window.headerNotificationManager = headerNotificationManager;
