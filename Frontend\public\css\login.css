* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background: #e8eaf6;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.container {
  max-width: 400px;
  width: 90%;
  background: #ffffff;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

h2 {
  text-align: center;
  color: #333;
  margin-bottom: 8px;
  font-size: 24px;
  font-weight: 600;
}

.subtitle {
  text-align: center;
  color: #666;
  margin-bottom: 30px;
  font-size: 14px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #333;
  font-size: 14px;
  font-weight: 500;
}

input[type="text"],
input[type="password"] {
  width: 100%;
  padding: 12px 16px;
  font-size: 14px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #fff;
  transition: border-color 0.3s ease;
  outline: none;
}

input[type="text"]:focus,
input[type="password"]:focus {
  border-color: #dc3545;
}

button {
  width: 100%;
  padding: 12px;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
  margin-top: 10px;
}

button:hover {
  background: #c82333;
}

p {
  text-align: center;
  margin-top: 20px;
  color: #666;
  font-size: 14px;
}

p a {
  color: #dc3545;
  text-decoration: none;
  font-weight: 500;
}

p a:hover {
  text-decoration: underline;
}

p.error {
  color: #dc3545;
  margin-bottom: 20px;
  text-align: center;
  background: #f8d7da;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #f5c6cb;
  font-size: 14px;
}

/* Responsive */
@media (max-width: 480px) {
  .container {
    padding: 30px 20px;
    margin: 20px;
  }

  h2 {
    font-size: 22px;
  }
}
  