doctype html
html(lang='vi')
  head
    meta(charset='UTF-8')
    meta(name='viewport' content='width=device-width, initial-scale=1.0')
    title ADMIN - Quản lí sách
    link(rel='stylesheet' href='/public/css/admin.css')
    link(rel='stylesheet' href='/public/css/admin-notification.css')
    link(rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css')
    script(src='/socket.io/socket.io.js')
  body
    include admin-header.pug
              i.fa-solid.fa-cart-shopping
              h4 Quản lí đơn hàng
          li
            a(href='/admin/sale')
              i.fa-solid.fa-sack-dollar
              h4 Quản lí doanh thu
          li
            a(href='/')
              i.fa-solid.fa-share-from-square
              h4 Quay về trang chủ
    .main
      .admin
        h2 Quản lí sách
    .capnhapsach
      i.fa-solid.fa-plus
      a(href='/admin/book/add')
        h4 ADD NEW BOOK
    .danhsach
      .danhsachthongtin
        h3 QUẢN LÍ SÁCH
        table.thongtin
          tr
            th STT
            th Tên sách
            th Ảnh
            th Thể loại
            th Giá
            th Tác giả
            th Thao tác
          if books && books.length > 0
            each book, index in books
              tr
                td= index + 1
                td= book.title
                td
                  if book.image
                    img(src=book.image width='60' alt=book.title)
                  else
                    span Không có ảnh
                td= book.category ? book.category.name : 'Chưa phân loại'
                td= book.price ? book.price.toLocaleString() + 'đ' : '0đ'
                td= book.author
                td
                  button.btn2(onclick=`editBook('${book._id}')`)
                    i.fa-solid.fa-pen
                  button.btn3(onclick=`deleteBook('${book._id}')`)
                    i.fa-solid.fa-trash-can
          else
            tr
              td(colspan="7" style="text-align: center; padding: 20px;") Không có dữ liệu sách
    script(src='/public/js/admin.js')
    script(src='/public/js/admin-notification.js')