/* Header Notification Styles */
.notify {
    position: relative;
}

.notify-bell {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ff4757;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 10px;
    font-weight: bold;
    min-width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.notify-content {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    width: 320px;
    max-height: 400px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
    margin-top: 10px;
}

.notify-content.show {
    display: block;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.notify-content li {
    border-bottom: 1px solid #f0f0f0;
    list-style: none;
}

.notify-content li:last-child {
    border-bottom: none;
}

.notify-content li a {
    display: flex;
    padding: 12px 15px;
    text-decoration: none;
    color: #333;
    transition: background-color 0.3s ease;
    align-items: flex-start;
    gap: 10px;
}

.notify-content li a:hover {
    background: #f8f9fa;
}

.notify-content li.unread a {
    background: #fff3cd;
    border-left: 3px solid #ffc107;
}

.notify-content img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    flex-shrink: 0;
}

.notify-content p {
    flex: 1;
    margin: 0;
    font-size: 13px;
    line-height: 1.4;
}

.notify-content span {
    color: #007bff;
    font-weight: 500;
}

.notification-loading {
    padding: 20px;
    text-align: center;
    color: #666;
    font-style: italic;
}

.notification-empty {
    padding: 30px 15px;
    text-align: center;
    color: #999;
    font-style: italic;
}

.notification-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.notification-title {
    font-weight: 600;
    font-size: 14px;
    color: #333;
}

.notification-time {
    font-size: 11px;
    color: #999;
}

.notification-message {
    font-size: 13px;
    color: #666;
    line-height: 1.4;
}

/* Responsive */
@media (max-width: 768px) {
    .notify-content {
        width: 280px;
        right: -50px;
    }
}

@media (max-width: 480px) {
    .notify-content {
        width: 260px;
        right: -100px;
        max-height: 300px;
    }
    
    .notify-content li a {
        padding: 10px 12px;
    }
    
    .notify-content img {
        width: 32px;
        height: 32px;
    }
}
