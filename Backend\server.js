// Tải các biến môi trường từ file .env
require('dotenv').config();

// Import các thư viện cần thiết
const express = require('express');
const cors = require('cors');
const path = require('path');
const cookieParser = require('cookie-parser');
const session = require('express-session');
const http = require('http');
const socketIo = require('socket.io');
const connectDB = require('./config/db');

// Import các routes
const authRoute = require('./routes/authRoute');
const bookRoute = require('./routes/bookRoute');
const searchRoute = require('./routes/searchRoute');
const cartRoute = require('./routes/cartRoute');
const userRoute = require('./routes/userRoute');
const chekoutRoute = require('./routes/checkoutRoute');
const orderRoute = require('./routes/orderRoute');
const reviewRoute = require('./routes/reviewRoute');
const adminRoute = require('./routes/adminRoute');

// Tạo ứng dụng Express và HTTP server
const app = express();
const server = http.createServer(app);

// Cấu hình Socket.IO
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

// Đặt cổng cho server
const PORT = process.env.PORT;

// Kết nối đến cơ sở dữ liệu MongoDB
connectDB();

// Cấu hình middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cors());
app.use(cookieParser());

// Cấu hình session
app.use(session({
  secret: process.env.SESSION_SECRET,
  resave: false,
  saveUninitialized: true,
  cookie: { maxAge: 3600000 } // 1 giờ
}));

// Middleware để load user info cho tất cả routes
app.use(async function(req, res, next) {
  try {
    const User = require('./models/userModel');
    const jwt = require('jsonwebtoken');

    const token = req.cookies.token;
    if (token) {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const user = await User.findById(decoded.id).select("-password");
      if (user) {
        req.user = user;
        res.locals.user = user;
      }
    }
  } catch (error) {
    // Không làm gì, chỉ tiếp tục mà không có user
  }
  next();
});

// Middleware để xử lý thông báo từ query parameters
app.use(function(req, res, next) {
  res.locals.success = req.query.success || null;
  res.locals.error = req.query.error || null;
  next();
});

// Cấu hình view engine
app.set('view engine', 'pug');
app.set('views', path.join(__dirname, '../Frontend/views'));

// Cấu hình thư mục tĩnh
app.use('/public', express.static(path.join(__dirname, '../Frontend/public')));
app.use('/uploads', express.static(path.join(__dirname, '../Frontend/public/uploads')));

// Route cho trang chủ
app.get('/', function(req, res) {
  res.redirect('/books/home');
});

app.get('/login', function(req, res) {
  res.redirect('/auth/login');
});

app.get('/register', function(req, res) {
  res.redirect('/auth/register');
});

app.get('/test-search', function(req, res) {
  res.render('test-search');
});

app.get('/debug-search', function(req, res) {
  res.render('debug-search');
});

app.get('/test-api', async function(req, res) {
  try {
    const Book = require('./models/bookModel');
    const books = await Book.find().limit(5).lean();
    const totalBooks = await Book.countDocuments();
    res.json({
      message: 'API working',
      totalBooks: totalBooks,
      sampleBooks: books.map(book => ({
        id: book._id,
        title: book.title,
        author: book.author,
        price: book.price
      }))
    });
  } catch (error) {
    res.json({ error: error.message });
  }
});

app.get('/test-home', async function(req, res) {
  try {
    console.log('Testing home page data...');
    const Book = require('./models/bookModel');
    const Category = require('./models/categoryModel');

    console.log('Fetching books by groups...');
    const banChay = await Book.find({ group: 'banchay' }).limit(8);
    console.log('banChay:', banChay.length);

    const manga = await Book.find({ group: 'manga' }).limit(8);
    console.log('manga:', manga.length);

    const categories = await Category.find().sort({ name: 1 });
    console.log('categories:', categories.length);

    res.json({
      message: 'Home data test successful',
      banChay: banChay.length,
      manga: manga.length,
      categories: categories.length
    });
  } catch (error) {
    console.error('Test home error:', error);
    res.json({ error: error.message });
  }
});

const notificationRoute = require('./routes/notificationRoute');
const userNotificationRoute = require('./routes/userNotificationRoute');

app.use('/auth', authRoute);
app.use('/books', bookRoute);
app.use('/search', searchRoute);
app.use('/cart', cartRoute);
app.use('/checkout', chekoutRoute);
app.use('/orders', orderRoute);
app.use('/reviews', reviewRoute);
app.use('/', userRoute);
app.use('/admin', adminRoute);
app.use('/api/notifications', notificationRoute);
app.use('/api/user-notifications', userNotificationRoute);



app.use(function(err, req, res, next) {
  console.error('Error occurred:', err.stack);
  console.error('Error message:', err.message);
  console.error('Request URL:', req.url);
  console.error('Request method:', req.method);
  res.status(500).send('Lỗi khi tải trang chủ');
});

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log('User connected:', socket.id);

  // Join admin room for admin users
  socket.on('join-admin', () => {
    socket.join('admin-room');
    console.log('Admin joined room:', socket.id);
  });

  // Join user room for regular users
  socket.on('join-user', (userId) => {
    socket.join(`user-${userId}`);
    console.log(`User ${userId} joined room:`, socket.id);
  });

  socket.on('disconnect', () => {
    console.log('User disconnected:', socket.id);
  });
});

// Làm io có thể truy cập từ các route khác
app.set('io', io);

server.listen(PORT, function() {
  console.log(`Server chạy tại http://localhost:${PORT}`);
});
