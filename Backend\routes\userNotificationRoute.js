const express = require('express');
const router = express.Router();
const userNotificationController = require('../controllers/userNotificationController');
const { auth } = require('../middleware/authMiddleware');

// Tất cả routes đều yêu cầu authentication
router.use(auth);

// GET /api/user-notifications - <PERSON><PERSON><PERSON> danh sách thông báo của user
router.get('/', userNotificationController.getUserNotifications);

// GET /api/user-notifications/unread-count - L<PERSON><PERSON> số thông báo chưa đọc
router.get('/unread-count', userNotificationController.getUnreadCount);

// PUT /api/user-notifications/:id/read - Đ<PERSON>h dấu đã đọc
router.put('/:id/read', userNotificationController.markAsRead);

// PUT /api/user-notifications/mark-all-read - <PERSON><PERSON><PERSON> dấu tất cả đã đọc
router.put('/mark-all-read', userNotificationController.markAllAsRead);

module.exports = router;
