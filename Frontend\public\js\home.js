// <PERSON><PERSON><PERSON> b<PERSON><PERSON> đã load hoàn toàn trước khi chạy JavaScript
document.addEventListener('DOMContentLoaded', function() {
    console.log('Home.js loaded successfully!'); // Debug log

    const slideContainer = document.getElementById("slide-container");
    const nextBtn = document.getElementById("nextBtn");
    const prevBtn = document.getElementById("prevBtn");

    // Kiểm tra xem các element có tồn tại không
    if (!slideContainer) {
        console.error('Không tìm thấy slide-container');
        return;
    }

    if (!nextBtn || !prevBtn) {
        console.error('Không tìm thấy nút next hoặc prev');
        return;
    }

    const totalSlides = slideContainer.querySelectorAll("img").length;
    let currentIndex = 0;

    console.log('Tổng số slide:', totalSlides); // Debug log

    function showSlide(index) {
        const offset = index * -1220; // mỗi ảnh rộng 1220px
        slideContainer.style.transform = `translateX(${offset}px)`;
        console.log('Chuyển đến slide:', index); // Debug log
    }

    function nextSlide() {
        currentIndex = (currentIndex + 1) % totalSlides;
        showSlide(currentIndex);
    }

    function prevSlide() {
        currentIndex = (currentIndex - 1 + totalSlides) % totalSlides;
        showSlide(currentIndex);
    }

    // Nút bấm
    nextBtn.addEventListener("click", nextSlide);
    prevBtn.addEventListener("click", prevSlide);

    // Chạy slide tự động mỗi 3 giây
    setInterval(nextSlide, 3000);

    console.log('Slider đã được khởi tạo thành công!'); // Debug log

    // Notification System
    class NotificationManager {
        constructor() {
            this.notificationBell = document.getElementById('notification-bell');
            this.notificationBadge = document.getElementById('notification-badge');
            this.notificationDropdown = document.getElementById('notification-dropdown');
            this.notificationList = document.getElementById('notification-list');
            this.markAllReadBtn = document.getElementById('mark-all-read');

            // Initialize Socket.IO for real-time notifications
            this.socket = null;
            this.initSocket();

            this.init();
        }

        initSocket() {
            try {
                this.socket = io();

                // Join user room if logged in
                const userElement = document.querySelector('[data-user-id]');
                if (userElement) {
                    const userId = userElement.getAttribute('data-user-id');
                    this.socket.emit('join-user', userId);
                }

                // Listen for new notifications
                this.socket.on('new-notification', (notification) => {
                    this.handleNewNotification(notification);
                });

                console.log('Socket.IO initialized for notifications');
            } catch (error) {
                console.log('Socket.IO not available, using polling only');
            }
        }

        handleNewNotification(notification) {
            // Show toast notification
            this.showToastNotification(notification);

            // Update badge count
            this.updateUnreadCount();

            // Reload notifications if dropdown is open
            if (this.notificationDropdown.style.display !== 'none') {
                this.loadNotifications();
            }
        }

        showToastNotification(notification) {
            // Create toast element
            const toast = document.createElement('div');
            toast.className = 'toast-notification success';
            toast.innerHTML = `
                <div class="toast-header">
                    <strong>${notification.title}</strong>
                    <button type="button" class="toast-close" onclick="this.parentElement.parentElement.remove()">×</button>
                </div>
                <div class="toast-body">
                    ${notification.message}
                </div>
            `;

            // Add to page
            document.body.appendChild(toast);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.remove();
                }
            }, 5000);
        }

        init() {
            if (!this.notificationBell) {
                console.log('Notification elements not found');
                return;
            }

            // Event listeners
            this.notificationBell.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleDropdown();
            });

            if (this.markAllReadBtn) {
                this.markAllReadBtn.addEventListener('click', () => {
                    this.markAllAsRead();
                });
            }

            // Close dropdown when clicking outside
            document.addEventListener('click', (e) => {
                if (!e.target.closest('.notification-container')) {
                    this.closeDropdown();
                }
            });

            // Load notifications initially
            this.loadNotifications();

            // Update notifications every 30 seconds
            setInterval(() => {
                this.updateUnreadCount();
            }, 30000);
        }

        toggleDropdown() {
            const isVisible = this.notificationDropdown.style.display === 'block';
            if (isVisible) {
                this.closeDropdown();
            } else {
                this.openDropdown();
            }
        }

        openDropdown() {
            this.notificationDropdown.style.display = 'block';
            this.loadNotifications();
        }

        closeDropdown() {
            this.notificationDropdown.style.display = 'none';
        }

        async loadNotifications() {
            try {
                // Check if user is admin by looking for admin-specific elements
                const isAdmin = window.location.pathname.includes('/admin') ||
                               document.querySelector('.admin') !== null ||
                               document.querySelector('[href*="/admin"]') !== null;

                // Use appropriate API endpoint based on user role
                const apiEndpoint = isAdmin ? '/api/notifications' : '/api/user-notifications';
                const response = await fetch(apiEndpoint);

                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        this.renderNotifications(data.notifications || []);
                        this.updateBadge(data.unreadCount || 0);
                    }
                } else {
                    // If API doesn't exist yet, show sample notifications
                    this.renderSampleNotifications();
                }
            } catch (error) {
                console.log('Loading sample notifications...');
                this.renderSampleNotifications();
            }
        }

        renderSampleNotifications() {
            const sampleNotifications = [
                {
                    _id: '1',
                    title: 'Đơn hàng đã được xác nhận',
                    message: 'Đơn hàng #DH001 của bạn đã được xác nhận và đang được chuẩn bị.',
                    createdAt: new Date(),
                    isRead: false,
                    type: 'order'
                },
                {
                    _id: '2',
                    title: 'Sách mới đã có sẵn',
                    message: 'Những cuốn sách mới nhất đã được cập nhật trong cửa hàng.',
                    createdAt: new Date(Date.now() - 3600000),
                    isRead: true,
                    type: 'system'
                }
            ];

            this.renderNotifications(sampleNotifications);
            this.updateBadge(1);
        }

        renderNotifications(notifications) {
            if (notifications.length === 0) {
                this.notificationList.innerHTML = '<div class="notification-empty">Không có thông báo mới</div>';
                return;
            }

            this.notificationList.innerHTML = notifications.map(notification => `
                <div class="notification-item ${!notification.isRead ? 'unread' : ''}"
                     data-id="${notification._id}"
                     data-type="${notification.type}"
                     data-order-id="${notification.data?.orderId || ''}"
                     onclick="notificationManager.handleNotificationClick('${notification._id}', '${notification.type}', '${notification.data?.orderId || ''}')">
                    <div class="notification-title">
                        ${notification.title}
                        <span class="notification-priority priority-medium">mới</span>
                    </div>
                    <div class="notification-message">${notification.message}</div>
                    <div class="notification-time">${this.formatTime(notification.createdAt)}</div>
                </div>
            `).join('');
        }

        async handleNotificationClick(notificationId, type, orderId) {
            // Mark as read first
            await this.markAsRead(notificationId);

            // Handle different notification types
            switch(type) {
                case 'new_order':
                case 'order_cancelled':
                    if (orderId) {
                        // Redirect to order management page with specific order
                        window.location.href = `/admin/order?orderId=${orderId}`;
                    } else {
                        // Redirect to general order management page
                        window.location.href = '/admin/order';
                    }
                    break;
                case 'order_confirmation':
                case 'order_status_update':
                    if (orderId) {
                        // Redirect to user's order page
                        window.location.href = `/customer/orders`;
                    } else {
                        // Redirect to general customer orders page
                        window.location.href = '/customer/orders';
                    }
                    break;
                case 'low_stock':
                    // Redirect to book management page
                    window.location.href = '/admin/book';
                    break;
                case 'system':
                case 'welcome':
                case 'personal':
                default:
                    // For system notifications, just mark as read
                    break;
            }

            // Close dropdown after click
            this.closeDropdown();
        }

        async markAsRead(notificationId) {
            try {
                // Update UI immediately
                const notificationItem = document.querySelector(`[data-id="${notificationId}"]`);
                if (notificationItem) {
                    notificationItem.classList.remove('unread');
                }

                // Check if user is admin and use appropriate API endpoint
                const isAdmin = window.location.pathname.includes('/admin') ||
                               document.querySelector('.admin') !== null ||
                               document.querySelector('[href*="/admin"]') !== null;

                const apiEndpoint = isAdmin ? '/api/notifications' : '/api/user-notifications';
                const response = await fetch(`${apiEndpoint}/${notificationId}/read`, {
                    method: 'PUT'
                });

                if (response.ok) {
                    this.updateUnreadCount();
                }
            } catch (error) {
                console.log('Notification marked as read locally');
            }
        }

        async markAllAsRead() {
            try {
                // Update UI immediately
                const unreadItems = document.querySelectorAll('.notification-item.unread');
                unreadItems.forEach(item => item.classList.remove('unread'));

                // Check if user is admin and use appropriate API endpoint
                const isAdmin = window.location.pathname.includes('/admin') ||
                               document.querySelector('.admin') !== null ||
                               document.querySelector('[href*="/admin"]') !== null;

                const apiEndpoint = isAdmin ? '/api/notifications' : '/api/user-notifications';
                const response = await fetch(`${apiEndpoint}/mark-all-read`, {
                    method: 'PUT'
                });

                if (response.ok) {
                    this.updateBadge(0);
                }
            } catch (error) {
                console.log('All notifications marked as read locally');
                this.updateBadge(0);
            }
        }

        async updateUnreadCount() {
            try {
                // Check if user is admin and use appropriate API endpoint
                const isAdmin = window.location.pathname.includes('/admin') ||
                               document.querySelector('.admin') !== null ||
                               document.querySelector('[href*="/admin"]') !== null;

                const apiEndpoint = isAdmin ? '/api/notifications' : '/api/user-notifications';
                const response = await fetch(`${apiEndpoint}/unread-count`);
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        this.updateBadge(data.unreadCount);
                    }
                }
            } catch (error) {
                console.log('Could not update unread count');
            }
        }

        updateBadge(count) {
            if (count > 0) {
                this.notificationBadge.textContent = count > 99 ? '99+' : count;
                this.notificationBadge.style.display = 'block';
            } else {
                this.notificationBadge.style.display = 'none';
            }
        }

        formatTime(dateString) {
            const date = new Date(dateString);
            const now = new Date();
            const diffInMinutes = Math.floor((now - date) / (1000 * 60));

            if (diffInMinutes < 1) return 'Vừa xong';
            if (diffInMinutes < 60) return `${diffInMinutes} phút trước`;
            if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)} giờ trước`;
            return `${Math.floor(diffInMinutes / 1440)} ngày trước`;
        }
    }

    // Initialize notification manager
    window.notificationManager = new NotificationManager();
});