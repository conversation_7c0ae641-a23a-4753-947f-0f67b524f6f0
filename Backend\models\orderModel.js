const mongoose = require('mongoose');

const orderSchema = new mongoose.Schema({
  orderCode: { type: String, required: true, unique: true },
  user: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  items: [
    {
      book: { type: mongoose.Schema.Types.ObjectId, ref: 'Book', required: true },
      quantity: { type: Number, required: true },
      price: { type: Number, required: true },
    }
  ],
  totalPrice: { type: Number, required: true },
  address: {
    fullname: String,
    phone: String,
    address: String
  },
  note: { type: String },
  paymentMethod: { type: String, enum: ['cod', 'bank'], default: 'cod' },
  status: {
    type: String,
    enum: ['pending', 'transporting', 'delivered', 'cancelled'],
    default: 'pending'
  },
  statusHistory: [{
    status: {
      type: String,
      enum: ['pending', 'transporting', 'delivered', 'cancelled']
    },
    changedAt: {
      type: Date,
      default: Date.now
    },
    changedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  }],
}, { timestamps: true });

// Middleware để tự động thêm trạng thái ban đầu vào lịch sử
orderSchema.pre('save', function(next) {
  if (this.isNew) {
    this.statusHistory = [{
      status: this.status,
      changedAt: new Date(),
      changedBy: this.user
    }];
  }
  next();
});

// Phương thức kiểm tra xem có thể thay đổi trạng thái không
orderSchema.methods.canChangeStatusTo = function(newStatus) {
  const currentStatus = this.status;

  // Nếu đã ở trạng thái cuối thì không thể thay đổi
  if (currentStatus === 'delivered' || currentStatus === 'cancelled') {
    return false;
  }

  // Định nghĩa các chuyển đổi trạng thái hợp lệ
  const validTransitions = {
    'pending': ['transporting', 'cancelled'],
    'transporting': ['delivered', 'cancelled']
  };

  return validTransitions[currentStatus] && validTransitions[currentStatus].includes(newStatus);
};

// Phương thức thay đổi trạng thái với kiểm tra
orderSchema.methods.changeStatus = function(newStatus, changedBy) {
  if (!this.canChangeStatusTo(newStatus)) {
    throw new Error(`Không thể thay đổi từ trạng thái "${this.status}" sang "${newStatus}"`);
  }

  // Thêm vào lịch sử
  this.statusHistory.push({
    status: newStatus,
    changedAt: new Date(),
    changedBy: changedBy
  });

  // Cập nhật trạng thái hiện tại
  this.status = newStatus;
};

module.exports = mongoose.model('Order', orderSchema);
