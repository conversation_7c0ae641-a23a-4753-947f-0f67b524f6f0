html
    head
        title <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON> b<PERSON>o
        link(rel="stylesheet", href="./public/css/main.css")
        link(rel="stylesheet", href="./public/css/grid.css")
        link(rel="stylesheet", href="./public/css/responsive.css")
        script(src='./public/js/logout.js')
        script(src='/socket.io/socket.io.js')
        link(rel="stylesheet", href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css")
        style.
            .notification-item {
                background: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                margin-bottom: 15px;
                padding: 15px;
                transition: all 0.3s ease;
                cursor: pointer;
            }
            .notification-item:hover {
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                border-color: #007bff;
            }
            .notification-item.unread {
                border-left: 4px solid #007bff;
                background: #f8f9ff;
            }
            .notification-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;
            }
            .notification-title {
                font-weight: bold;
                color: #333;
                font-size: 16px;
            }
            .notification-time {
                color: #666;
                font-size: 12px;
            }
            .notification-message {
                color: #555;
                line-height: 1.4;
                margin-bottom: 8px;
            }
            .notification-priority {
                display: inline-block;
                padding: 2px 8px;
                border-radius: 12px;
                font-size: 10px;
                font-weight: bold;
                color: white;
            }
            .priority-low { background: #28a745; }
            .priority-medium { background: #ffc107; }
            .priority-high { background: #fd7e14; }
            .priority-urgent { background: #dc3545; }
            .loading-spinner {
                text-align: center;
                padding: 40px;
                color: #666;
            }
            .empty-state {
                text-align: center;
                padding: 60px 20px;
                color: #999;
            }
            .empty-state i {
                font-size: 48px;
                margin-bottom: 15px;
                color: #ddd;
            }
    body
        include ../header.pug 
        div(id="customer-header")
            div
                button(class="fa-solid fa-arrow-left" onclick="window.history.back()")
                p Tài khoản
        div(id="customer")
            div(class="customer-left")
                div
                    div
                        button(class="log-out") Đăng xuất
                        ul
                            li: a(href="/customer") Thông tin tài khoản
                            li: a(href="/customer/address") Thông tin địa chỉ
                            li: a(href="/customer/change-password") Đổi mật khẩu
                            li: a(href="/customer/orders") Đơn hàng của bạn
                            li(class="active" data-user-id=user._id): a(href="/customer/notifications") Thông báo
            div(class="customer-right")
                div
                    h2(style="margin-bottom: 20px; color: #333;") Thông báo của bạn
                    
                    // Loading state
                    div(id="notifications-loading" class="loading-spinner")
                        i(class="fa-solid fa-spinner fa-spin" style="font-size: 24px;")
                        p(style="margin-top: 10px;") Đang tải thông báo...
                    
                    // Empty state
                    div(id="notifications-empty" class="empty-state" style="display: none;")
                        i(class="fa-solid fa-bell")
                        p Bạn chưa có thông báo nào
                        small Các thông báo về đơn hàng và cập nhật sẽ hiển thị tại đây
                    
                    // Notifications container
                    div(id="notifications-container")
                    
                    // Pagination
                    div(id="pagination-container" class="listpage" style="display: none;")
                        ul(id="pagination-list")
        include ../footer.pug
    
    script.
        class CustomerNotificationManager {
            constructor() {
                this.currentPage = 1;
                this.totalPages = 1;
                this.socket = null;
                this.init();
            }

            init() {
                this.initSocket();
                this.loadNotifications();
            }

            initSocket() {
                try {
                    this.socket = io();
                    
                    // Join user room if logged in
                    const userElement = document.querySelector('[data-user-id]');
                    if (userElement) {
                        const userId = userElement.getAttribute('data-user-id');
                        this.socket.emit('join-user', userId);
                    }

                    // Listen for new notifications
                    this.socket.on('new-notification', (notification) => {
                        this.handleNewNotification(notification);
                    });

                    console.log('Socket.IO initialized for customer notifications');
                } catch (error) {
                    console.log('Socket.IO not available');
                }
            }

            handleNewNotification(notification) {
                // Reload notifications to show the new one
                this.loadNotifications();
                
                // Show toast notification
                this.showToast(notification.title, notification.message);
            }

            showToast(title, message) {
                const toast = document.createElement('div');
                toast.className = 'toast-notification success';
                toast.innerHTML = `
                    <div class="toast-header">
                        <strong>${title}</strong>
                        <button type="button" class="toast-close" onclick="this.parentElement.parentElement.remove()">×</button>
                    </div>
                    <div class="toast-body">${message}</div>
                `;
                document.body.appendChild(toast);
                setTimeout(() => toast.remove(), 5000);
            }

            async loadNotifications(page = 1) {
                try {
                    this.showLoading();
                    
                    const response = await fetch(`/api/user-notifications?page=${page}&limit=10`);
                    const data = await response.json();

                    if (data.success) {
                        this.currentPage = data.page;
                        this.totalPages = data.pages;
                        this.renderNotifications(data.notifications);
                        this.renderPagination();
                    } else {
                        this.showEmpty();
                    }
                } catch (error) {
                    console.error('Error loading notifications:', error);
                    this.showEmpty();
                }
            }

            showLoading() {
                document.getElementById('notifications-loading').style.display = 'block';
                document.getElementById('notifications-empty').style.display = 'none';
                document.getElementById('notifications-container').innerHTML = '';
                document.getElementById('pagination-container').style.display = 'none';
            }

            showEmpty() {
                document.getElementById('notifications-loading').style.display = 'none';
                document.getElementById('notifications-empty').style.display = 'block';
                document.getElementById('notifications-container').innerHTML = '';
                document.getElementById('pagination-container').style.display = 'none';
            }

            renderNotifications(notifications) {
                document.getElementById('notifications-loading').style.display = 'none';
                document.getElementById('notifications-empty').style.display = 'none';
                
                if (notifications.length === 0) {
                    this.showEmpty();
                    return;
                }

                const container = document.getElementById('notifications-container');
                container.innerHTML = notifications.map(notification => `
                    <div class="notification-item ${!notification.isRead ? 'unread' : ''}" 
                         onclick="customerNotificationManager.handleNotificationClick('${notification._id}', '${notification.type}', '${notification.data?.orderId || ''}')">
                        <div class="notification-header">
                            <div class="notification-title">${notification.title}</div>
                            <div class="notification-time">${this.formatTime(notification.createdAt)}</div>
                        </div>
                        <div class="notification-message">${notification.message}</div>
                        <div style="margin-top: 8px;">
                            <span class="notification-priority priority-${notification.priority}">${this.getPriorityText(notification.priority)}</span>
                        </div>
                    </div>
                `).join('');
            }

            renderPagination() {
                if (this.totalPages <= 1) return;

                document.getElementById('pagination-container').style.display = 'block';
                const paginationList = document.getElementById('pagination-list');
                
                let paginationHTML = '';
                
                // Previous button
                if (this.currentPage > 1) {
                    paginationHTML += `<li><a href="#" onclick="customerNotificationManager.loadNotifications(${this.currentPage - 1})"><i class="fa-solid fa-chevron-left"></i></a></li>`;
                }
                
                // Page numbers
                for (let i = 1; i <= this.totalPages; i++) {
                    if (i === this.currentPage) {
                        paginationHTML += `<li class="active"><a href="#">${i}</a></li>`;
                    } else {
                        paginationHTML += `<li><a href="#" onclick="customerNotificationManager.loadNotifications(${i})">${i}</a></li>`;
                    }
                }
                
                // Next button
                if (this.currentPage < this.totalPages) {
                    paginationHTML += `<li><a href="#" onclick="customerNotificationManager.loadNotifications(${this.currentPage + 1})"><i class="fa-solid fa-chevron-right"></i></a></li>`;
                }
                
                paginationList.innerHTML = paginationHTML;
            }

            async handleNotificationClick(notificationId, type, orderId) {
                // Mark as read
                await this.markAsRead(notificationId);

                // Handle different notification types
                switch(type) {
                    case 'order_confirmation':
                        // Redirect to pending orders (newly placed orders)
                        window.location.href = '/customer/orders?status=pending';
                        break;
                    case 'order_status_update':
                        // Redirect to all orders to see the updated status
                        window.location.href = '/customer/orders';
                        break;
                    case 'order_cancelled':
                        // Redirect to cancelled orders
                        window.location.href = '/customer/orders?status=cancelled';
                        break;
                    default:
                        // Just mark as read for other types
                        break;
                }
            }

            async markAsRead(notificationId) {
                try {
                    await fetch(`/api/user-notifications/${notificationId}/read`, {
                        method: 'PUT'
                    });
                    
                    // Update UI
                    const notificationElement = document.querySelector(`[onclick*="${notificationId}"]`);
                    if (notificationElement) {
                        notificationElement.classList.remove('unread');
                    }
                } catch (error) {
                    console.error('Error marking notification as read:', error);
                }
            }

            formatTime(dateString) {
                const date = new Date(dateString);
                const now = new Date();
                const diffMs = now - date;
                const diffMins = Math.floor(diffMs / 60000);
                const diffHours = Math.floor(diffMs / 3600000);
                const diffDays = Math.floor(diffMs / 86400000);

                if (diffMins < 1) return 'Vừa xong';
                if (diffMins < 60) return `${diffMins} phút trước`;
                if (diffHours < 24) return `${diffHours} giờ trước`;
                if (diffDays < 7) return `${diffDays} ngày trước`;
                
                return date.toLocaleDateString('vi-VN');
            }

            getPriorityText(priority) {
                const priorityMap = {
                    'low': 'Thấp',
                    'medium': 'Trung bình', 
                    'high': 'Cao',
                    'urgent': 'Khẩn cấp'
                };
                return priorityMap[priority] || 'Trung bình';
            }
        }

        // Initialize when page loads
        const customerNotificationManager = new CustomerNotificationManager();
