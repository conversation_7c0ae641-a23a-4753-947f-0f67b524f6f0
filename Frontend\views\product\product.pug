doctype html
html(lang="vi")
    head
        title= book ? book.title : "Sản phẩm"
        meta(charset="UTF-8")
        meta(name="viewport", content="width=device-width, initial-scale=1.0")
        link(rel="stylesheet", href="/public/css/main.css")
        link(rel="stylesheet", href="/public/css/responsive.css")
        link(rel="stylesheet", href="/public/css/col.css")
        link(rel="stylesheet", href="/public/css/style.css")
        link(rel="stylesheet", href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css")
    body
        include ../header.pug
        div#product-header
            //-Quay lại trang chủ
            div
                a(href="/")
                    button.fa-solid.fa-arrow-left
                p Sản phẩm
        div#product
            div.container
                div.product-left
                    div
                        img(src=book.image || "/public/img/default.jpg", alt=book.title)

                div.product-right
                    div.product-info
                        h1= book.title
                        div
                            i.fa-solid.fa-star
                                span= averageRating || '0'
                            p Đã bán
                                span= book.sold || 0
                        h3= `${book.price.toLocaleString('vi-VN')} đ`

                        // Hiển thị trạng thái tồn kho
                        div.stock-status
                            if book.stock <= 0
                                p.out-of-stock
                                    i.fa-solid.fa-exclamation-triangle
                                    span  Sản phẩm đã hết hàng
                            else if book.stock <= 5
                                p.low-stock
                                    i.fa-solid.fa-exclamation-circle
                                    span  Chỉ còn #{book.stock} sản phẩm
                            else
                                p.in-stock
                                    i.fa-solid.fa-check-circle
                                    span  Còn hàng (#{book.stock} sản phẩm)

                        if book.stock > 0
                            form#add-to-cart-form(data-book-id=book._id)
                                div.quantity-section
                                    label(for="quantity") Số lượng:
                                    div.quantity-controls
                                        button.quantity-btn.decrease-btn.fa-solid.fa-minus#decrease-qty(type="button", title="Giảm số lượng")
                                        input#quantity(type="text", name="quantity", value="1", min="1", max=book.stock, data-stock=book.stock)
                                        button.quantity-btn.increase-btn.fa-solid.fa-plus#increase-qty(type="button", title="Tăng số lượng")
                                    div.stock-info
                                        span.stock-available Còn lại:
                                        span.stock-number= book.stock
                                        span.stock-unit sản phẩm
                                div.prd-buy
                                    button#add-to-cart-btn(type="button")
                                        i.fa-solid.fa-cart-shopping
                                        p Thêm vào giỏ hàng
                                    button#buy-now-btn(type="button") Mua Ngay
                        else
                            div.out-of-stock-message
                                h4 Sản phẩm đã hết hàng
                    div.details-tb
                        h2 Thông tin chi tiết
                        table
                            tr
                                td Mã sách
                                td= book.code || 'Không có thông tin'
                            tr
                                td Tên nhà cung cấp
                                td= book.supplier || 'Không có thông tin'
                            tr
                                td Tác giả
                                td= book.author || 'Không có thông tin'
                            tr
                                td NXB
                                td= book.publisher || 'Không có thông tin'
                            tr
                                td Năm XB
                                td= book.year || 'Không có thông tin'
                            tr
                                td Ngôn Ngữ
                                td= book.language || 'Tiếng Việt'
                            tr
                                td Số trang
                                td= book.pageCount || 'Không có thông tin'
                            tr
                                td Hình thức
                                td= book.format || 'Không có thông tin'
                    div.product-desc
                        h2 Mô tả sản phẩm
                        h3= book.title
                        if book && book.description
                            p.desc.collapse= book.description
                        else
                            p.desc.collapse
                                | Chưa có mô tả cho sản phẩm này.
                        div
                            button.read-more-btn Xem thêm

        if book.stock > 0
            div#product-add
                form#mobile-add-to-cart-form(data-book-id=book._id)
                    div.quantity
                        button.quantity-btn.decrease-btn.fa-solid.fa-minus#mobile-decrease-qty(type="button", title="Giảm số lượng")
                        input#mobile-quantity(type="text", name="quantity", value="1", min="1", max=book.stock, data-stock=book.stock)
                        button.quantity-btn.increase-btn.fa-solid.fa-plus#mobile-increase-qty(type="button", title="Tăng số lượng")
                    div.btn-add: button#mobile-add-to-cart-btn(type="button") Thêm vào giỏ hàng
                    div.btn-buy: button#mobile-buy-now-btn(type="button") Mua Ngay
        else
            div#product-add.out-of-stock-mobile
                p Sản phẩm đã hết hàng
        div.product-review#reviews
            div.container
                h2 Đánh giá sản phẩm
                div.review-rating
                    div
                        p= averageRating || '0.0'
                            span trên 5
                        div.star
                            - const fullStars = Math.floor(averageRating || 0)
                            - const hasHalfStar = (averageRating || 0) - fullStars >= 0.5
                            - for (let i = 1; i <= 5; i++)
                                if i <= fullStars
                                    i.fa-solid.fa-star
                                else if i === fullStars + 1 && hasHalfStar
                                    i.fa-solid.fa-star-half-stroke
                                else
                                    i.fa-regular.fa-star
                    ul
                        li(class=!currentRating ? 'active' : ''): a(href=`/books/${book._id}`) Tất cả (#{allReviews ? allReviews.length : 0})
                        li(class=currentRating === 5 ? 'active' : ''): a(href=`/books/${book._id}?rating=5`) 5 sao (#{allReviews ? allReviews.filter(r => r.rating === 5).length : 0})
                        li(class=currentRating === 4 ? 'active' : ''): a(href=`/books/${book._id}?rating=4`) 4 sao (#{allReviews ? allReviews.filter(r => r.rating === 4).length : 0})
                        li(class=currentRating === 3 ? 'active' : ''): a(href=`/books/${book._id}?rating=3`) 3 sao (#{allReviews ? allReviews.filter(r => r.rating === 3).length : 0})
                        li(class=currentRating === 2 ? 'active' : ''): a(href=`/books/${book._id}?rating=2`) 2 sao (#{allReviews ? allReviews.filter(r => r.rating === 2).length : 0})
                        li(class=currentRating === 1 ? 'active' : ''): a(href=`/books/${book._id}?rating=1`) 1 sao (#{allReviews ? allReviews.filter(r => r.rating === 1).length : 0})
                div.review-mess
                    if reviews && reviews.length > 0
                        each review in reviews
                            - const isUserReview = userReview && review._id.toString() === userReview._id.toString()
                            div.mess(class=isUserReview ? 'user-review' : '', style=isUserReview ? 'border: 2px solid #007bff; background-color: #f8f9fa;' : '')
                                h5= review.user ? review.user.username : 'Người dùng ẩn danh'
                                    if isUserReview
                                        span.badge(style="margin-left: 10px; background-color: #007bff; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px;") Đánh giá của bạn
                                div.star-mess
                                    - for (let i = 1; i <= 5; i++)
                                        if i <= review.rating
                                            i.fa-solid.fa-star
                                        else
                                            i.fa-regular.fa-star
                                if review.title
                                    h4(style="margin: 10px 0; font-weight: bold;")= review.title
                                p= review.comment
                    else
                        div.mess
                            p.text-center Chưa có đánh giá nào cho sản phẩm này.
                if reviewPagination && reviewPagination.totalPages > 1
                    div.listpage
                        ul
                            if reviewPagination.hasPrevPage
                                li: a(href=`/books/${book._id}?page=${reviewPagination.prevPage}${currentRating ? `&rating=${currentRating}` : ''}`): i.fa-solid.fa-chevron-left

                            - for (let i = 1; i <= reviewPagination.totalPages; i++)
                                li(class=i === reviewPagination.page ? 'active' : ''): a(href=`/books/${book._id}?page=${i}${currentRating ? `&rating=${currentRating}` : ''}`)= i

                            if reviewPagination.hasNextPage
                                li: a(href=`/books/${book._id}?page=${reviewPagination.nextPage}${currentRating ? `&rating=${currentRating}` : ''}`): i.fa-solid.fa-chevron-right
        include ../footer.pug
    script(src='/public/js/search.js')

    style.
      /* CSS cho quantity controls */
      .quantity-section {
        margin: 20px 0;
      }

      .quantity-section label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: #333;
        font-size: 16px;
      }

      .quantity-controls {
        display: flex;
        align-items: center;
        border: 2px solid #e0e0e0;
        border-radius: 8px;
        overflow: hidden;
        width: fit-content;
        background: white;
        transition: all 0.3s ease;
      }

      .quantity-controls:hover {
        border-color: #c92127;
        box-shadow: 0 0 0 3px rgba(201, 33, 39, 0.1);
      }

      .quantity-controls.max-reached {
        border-color: #ff6b6b;
        background-color: #fff5f5;
      }

      .quantity-btn {
        background: #f8f9fa;
        border: none;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
        color: #666;
        font-size: 14px;
      }

      .quantity-btn:hover {
        background: #c92127;
        color: white;
      }

      .quantity-btn:disabled {
        background: #e9ecef;
        color: #adb5bd;
        cursor: not-allowed;
      }

      .quantity-btn.max-reached {
        background: #ff6b6b;
        color: white;
        animation: pulse 0.5s ease-in-out;
      }

      @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
      }

      #quantity, #mobile-quantity {
        border: none;
        width: 60px;
        height: 40px;
        text-align: center;
        font-size: 16px;
        font-weight: 600;
        color: #333;
        background: white;
        outline: none;
      }

      #quantity:focus, #mobile-quantity:focus {
        background: #f8f9fa;
      }

      .stock-info {
        margin-top: 8px;
        font-size: 14px;
        color: #666;
        display: flex;
        align-items: center;
        gap: 4px;
      }

      .stock-number {
        font-weight: 600;
        color: #28a745;
      }

      .stock-number.low-stock {
        color: #ffc107;
      }

      .stock-number.very-low-stock {
        color: #dc3545;
      }

      /* Tooltip cho thông báo */
      .quantity-tooltip {
        position: absolute;
        background: #333;
        color: white;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 12px;
        white-space: nowrap;
        z-index: 1000;
        opacity: 0;
        transform: translateY(-10px);
        transition: all 0.3s ease;
        pointer-events: none;
      }

      .quantity-tooltip.show {
        opacity: 1;
        transform: translateY(0);
      }

      .quantity-tooltip::after {
        content: '';
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        border: 5px solid transparent;
        border-top-color: #333;
      }

      /* Custom alert styles */
      .custom-alert {
        position: fixed;
        top: 20px;
        right: 20px;
        background: #fff;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 16px 20px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10000;
        max-width: 350px;
        animation: slideIn 0.3s ease-out;
      }

      .custom-alert.error {
        border-left: 4px solid #dc3545;
        background: #fff5f5;
      }

      .custom-alert.warning {
        border-left: 4px solid #ffc107;
        background: #fffbf0;
      }

      .custom-alert.success {
        border-left: 4px solid #28a745;
        background: #f0fff4;
      }

      @keyframes slideIn {
        from {
          transform: translateX(100%);
          opacity: 0;
        }
        to {
          transform: translateX(0);
          opacity: 1;
        }
      }

      .alert-icon {
        margin-right: 8px;
        font-size: 16px;
      }

      .alert-message {
        font-size: 14px;
        line-height: 1.4;
      }

      /* Mobile responsive */
      @media (max-width: 768px) {
        .quantity-controls {
          width: 120px;
        }

        .custom-alert {
          top: 10px;
          right: 10px;
          left: 10px;
          max-width: none;
        }
      }

    script.
      document.addEventListener("DOMContentLoaded", () => {
        // Kiểm tra nếu URL có anchor #reviews thì scroll đến phần đánh giá
        if (window.location.hash === '#reviews') {
          const reviewsSection = document.getElementById('reviews');
          if (reviewsSection) {
            reviewsSection.scrollIntoView({ behavior: 'smooth' });
          }
        }

        //XEM THÊM
        const desc = document.querySelector('.desc');
        const btn = document.querySelector('.read-more-btn');

        if (btn && desc) {
          btn.addEventListener("click", function(){
            if (desc.classList.contains('collapse')) {
              desc.classList.remove('collapse');
              desc.classList.add('expanded');
              btn.textContent = 'Thu gọn';
            } else {
              desc.classList.remove('expanded');
              desc.classList.add('collapse');
              btn.textContent = 'Xem thêm';
            }
          });
        }

        // Custom alert function
        function showCustomAlert(message, type = 'warning') {
          // Remove existing alerts
          const existingAlerts = document.querySelectorAll('.custom-alert');
          existingAlerts.forEach(alert => alert.remove());

          const alert = document.createElement('div');
          alert.className = `custom-alert ${type}`;

          const icon = type === 'error' ? 'fa-exclamation-triangle' :
                      type === 'warning' ? 'fa-exclamation-circle' : 'fa-check-circle';

          alert.innerHTML = `
            <div style="display: flex; align-items: center;">
              <i class="fa-solid ${icon} alert-icon"></i>
              <div class="alert-message">${message}</div>
            </div>
          `;

          document.body.appendChild(alert);

          // Auto remove after 4 seconds
          setTimeout(() => {
            if (alert.parentNode) {
              alert.style.animation = 'slideIn 0.3s ease-out reverse';
              setTimeout(() => alert.remove(), 300);
            }
          }, 4000);
        }

        // Xử lý số lượng sản phẩm
        const quantityInput = document.getElementById('quantity');
        const mobileQuantityInput = document.getElementById('mobile-quantity');
        const increaseBtn = document.getElementById('increase-qty');
        const decreaseBtn = document.getElementById('decrease-qty');
        const mobileIncreaseBtn = document.getElementById('mobile-increase-qty');
        const mobileDecreaseBtn = document.getElementById('mobile-decrease-qty');

        function updateStockDisplay(input) {
          const currentValue = parseInt(input.value) || 1;
          const maxStock = parseInt(input.getAttribute('data-stock')) || 999;
          const stockNumber = document.querySelector('.stock-number');
          const quantityControls = input.closest('.quantity-controls');

          if (stockNumber) {
            const remaining = maxStock - currentValue;
            stockNumber.textContent = remaining;

            // Update stock color based on remaining quantity
            stockNumber.className = 'stock-number';
            if (remaining <= 2) {
              stockNumber.classList.add('very-low-stock');
            } else if (remaining <= 5) {
              stockNumber.classList.add('low-stock');
            }
          }

          // Update button states
          if (quantityControls) {
            if (currentValue >= maxStock) {
              quantityControls.classList.add('max-reached');
              const increaseButton = quantityControls.querySelector('.increase-btn');
              if (increaseButton) {
                increaseButton.classList.add('max-reached');
                increaseButton.disabled = true;
              }
            } else {
              quantityControls.classList.remove('max-reached');
              const increaseButton = quantityControls.querySelector('.increase-btn');
              if (increaseButton) {
                increaseButton.classList.remove('max-reached');
                increaseButton.disabled = false;
              }
            }

            const decreaseButton = quantityControls.querySelector('.decrease-btn');
            if (decreaseButton) {
              decreaseButton.disabled = currentValue <= 1;
            }
          }
        }

        function updateQuantity(input, change) {
          let value = parseInt(input.value) || 1;
          const maxStock = parseInt(input.getAttribute('max')) || 999;

          value += change;
          if (value < 1) {
            value = 1;
          }
          if (value > maxStock) {
            value = maxStock;
            if (change > 0) {
              showCustomAlert(`Số lượng tối đa có thể chọn là ${maxStock} sản phẩm`, 'warning');

              // Add visual feedback
              const quantityControls = input.closest('.quantity-controls');
              if (quantityControls) {
                quantityControls.classList.add('max-reached');
                setTimeout(() => {
                  quantityControls.classList.remove('max-reached');
                }, 1000);
              }
            }
          }

          input.value = value;
          updateStockDisplay(input);
        }

        if (increaseBtn) {
          increaseBtn.addEventListener('click', function() {
            updateQuantity(quantityInput, 1);
          });
        }

        if (decreaseBtn) {
          decreaseBtn.addEventListener('click', function() {
            updateQuantity(quantityInput, -1);
          });
        }

        if (mobileIncreaseBtn) {
          mobileIncreaseBtn.addEventListener('click', function() {
            updateQuantity(mobileQuantityInput, 1);
          });
        }

        if (mobileDecreaseBtn) {
          mobileDecreaseBtn.addEventListener('click', function() {
            updateQuantity(mobileQuantityInput, -1);
          });
        }

        // Validation khi người dùng nhập trực tiếp
        function validateQuantityInput(input) {
          const value = parseInt(input.value) || 1;
          const maxStock = parseInt(input.getAttribute('max')) || 999;

          if (value < 1) {
            input.value = 1;
            showCustomAlert('Số lượng phải ít nhất là 1', 'warning');
          } else if (value > maxStock) {
            input.value = maxStock;
            showCustomAlert(`Số lượng tối đa có thể chọn là ${maxStock} sản phẩm`, 'warning');
          }

          updateStockDisplay(input);
        }

        if (quantityInput) {
          quantityInput.addEventListener('blur', function() {
            validateQuantityInput(this);
          });
          quantityInput.addEventListener('input', function() {
            // Chỉ cho phép nhập số
            this.value = this.value.replace(/[^0-9]/g, '');
            updateStockDisplay(this);
          });

          // Initialize display
          updateStockDisplay(quantityInput);
        }

        if (mobileQuantityInput) {
          mobileQuantityInput.addEventListener('blur', function() {
            validateQuantityInput(this);
          });
          mobileQuantityInput.addEventListener('input', function() {
            // Chỉ cho phép nhập số
            this.value = this.value.replace(/[^0-9]/g, '');
            updateStockDisplay(this);
          });

          // Initialize display
          updateStockDisplay(mobileQuantityInput);
        }

        // Xử lý thêm vào giỏ hàng
        const addToCartForm = document.getElementById('add-to-cart-form');
        const mobileAddToCartForm = document.getElementById('mobile-add-to-cart-form');
        const addToCartBtn = document.getElementById('add-to-cart-btn');
        const mobileAddToCartBtn = document.getElementById('mobile-add-to-cart-btn');
        const buyNowBtn = document.getElementById('buy-now-btn');
        const mobileBuyNowBtn = document.getElementById('mobile-buy-now-btn');

        async function addToCart(form, redirect = false) {
          if (!form) {
            alert('Lỗi: Form không tồn tại');
            return;
          }

          const bookId = form.dataset.bookId;
          if (!bookId) {
            alert('Không thể thêm sản phẩm vào giỏ hàng');
            return;
          }

          const quantity = form.querySelector('input[name="quantity"]').value || 1;

          try {
            const response = await fetch(`/cart/add/${bookId}`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({ quantity })
            });

            if (response.ok) {
              if (redirect) {
                window.location.href = '/cart';
              } else {
                showCustomAlert('Đã thêm sản phẩm vào giỏ hàng', 'success');
              }
            } else {
              // Xử lý các loại lỗi khác nhau
              const errorData = await response.json();

              if (errorData.outOfStock) {
                showCustomAlert('Sản phẩm đã hết hàng!', 'error');
              } else if (errorData.canAdd !== undefined) {
                if (errorData.canAdd === 0) {
                  showCustomAlert(`Bạn đã có ${errorData.currentInCart} sản phẩm trong giỏ hàng và kho chỉ còn ${errorData.availableStock} sản phẩm!`, 'warning');
                } else {
                  showCustomAlert(`Bạn đã có ${errorData.currentInCart} sản phẩm trong giỏ hàng. Chỉ có thể thêm tối đa ${errorData.canAdd} sản phẩm nữa!`, 'warning');
                  // Tự động điều chỉnh số lượng input về số có thể thêm
                  const quantityInput = form.querySelector('input[name="quantity"]');
                  if (quantityInput && errorData.canAdd > 0) {
                    quantityInput.value = errorData.canAdd;
                    quantityInput.max = errorData.canAdd;
                    updateStockDisplay(quantityInput);
                  }
                }
              } else if (errorData.availableStock) {
                showCustomAlert(`Chỉ còn ${errorData.availableStock} sản phẩm trong kho!`, 'warning');
              } else if (errorData.lastItem) {
                showCustomAlert('Sản phẩm này chỉ còn 1 và bạn đã có trong giỏ hàng!', 'warning');
              } else {
                showCustomAlert(errorData.message || 'Không thể thêm sản phẩm vào giỏ hàng', 'error');
              }
            }
          } catch (error) {
            console.error('Error:', error);
            showCustomAlert('Đã xảy ra lỗi khi thêm sản phẩm vào giỏ hàng', 'error');
          }
        }

        if (addToCartBtn) {
          addToCartBtn.addEventListener('click', function() {
            addToCart(addToCartForm);
          });
        }

        if (mobileAddToCartBtn) {
          mobileAddToCartBtn.addEventListener('click', function() {
            addToCart(mobileAddToCartForm);
          });
        }

        if (buyNowBtn) {
          buyNowBtn.addEventListener('click', function() {
            addToCart(addToCartForm, true);
          });
        }

        if (mobileBuyNowBtn) {
          mobileBuyNowBtn.addEventListener('click', function() {
            addToCart(mobileAddToCartForm, true);
          });
        }
      });
