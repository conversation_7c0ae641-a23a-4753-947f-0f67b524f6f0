const express = require('express');
const router = express.Router();
const notificationController = require('../controllers/notificationController');
const { auth, isAdmin } = require('../middleware/authMiddleware');

// Tất cả routes đều yêu cầu admin
router.use(auth);
router.use(isAdmin);

// GET /api/notifications - <PERSON><PERSON><PERSON> danh sách thông báo
router.get('/', notificationController.getNotifications);

// GET /api/notifications/unread-count - <PERSON><PERSON><PERSON> số thông báo chưa đọc
router.get('/unread-count', notificationController.getUnreadCount);

// PUT /api/notifications/:id/read - Đ<PERSON>h dấu đã đọc
router.put('/:id/read', notificationController.markAsRead);

// PUT /api/notifications/mark-all-read - <PERSON><PERSON><PERSON> dấu tất cả đã đọc
router.put('/mark-all-read', notificationController.markAllAsRead);

module.exports = router;
