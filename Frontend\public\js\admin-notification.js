// Admin Notification System
class AdminNotificationManager {
  constructor() {
    this.socket = io();
    this.notificationBell = document.getElementById('notification-bell');
    this.notificationBadge = document.getElementById('notification-badge');
    this.notificationDropdown = document.getElementById('notification-dropdown');
    this.notificationList = document.getElementById('notification-list');
    this.markAllReadBtn = document.getElementById('mark-all-read');
    
    this.init();
  }

  init() {
    this.setupEventListeners();
    this.loadNotifications();
    this.setupSocketListeners();
  }

  setupEventListeners() {
    // Toggle dropdown
    if (this.notificationBell) {
      this.notificationBell.addEventListener('click', (e) => {
        e.stopPropagation();
        this.toggleDropdown();
      });
    }

    // Mark all as read
    if (this.markAllReadBtn) {
      this.markAllReadBtn.addEventListener('click', () => {
        this.markAllAsRead();
      });
    }

    // Close dropdown when clicking outside
    document.addEventListener('click', (e) => {
      if (!e.target.closest('.notification-container')) {
        this.closeDropdown();
      }
    });
  }

  toggleDropdown() {
    if (this.notificationDropdown) {
      this.notificationDropdown.classList.toggle('show');
      if (this.notificationDropdown.classList.contains('show')) {
        this.loadNotifications();
      }
    }
  }

  closeDropdown() {
    if (this.notificationDropdown) {
      this.notificationDropdown.classList.remove('show');
    }
  }

  async loadNotifications() {
    try {
      const response = await fetch('/api/notifications');
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          this.renderNotifications(data.notifications || []);
          this.updateBadge(data.unreadCount || 0);
        }
      } else {
        this.renderError('Không thể tải thông báo');
      }
    } catch (error) {
      console.error('Lỗi tải thông báo:', error);
      this.renderError('Lỗi kết nối');
    }
  }

  renderNotifications(notifications) {
    if (!this.notificationList) return;

    if (notifications.length === 0) {
      this.notificationList.innerHTML = '<div class="notification-empty">Không có thông báo mới</div>';
      return;
    }

    this.notificationList.innerHTML = notifications.map(notification => `
      <div class="notification-item ${!notification.isRead ? 'unread' : ''}" 
           data-id="${notification._id}"
           onclick="adminNotificationManager.handleNotificationClick('${notification._id}', '${notification.type}', '${notification.data?.orderId || ''}')">
        <div class="notification-icon ${notification.type}">
          <i class="fa-solid ${this.getNotificationIcon(notification.type)}"></i>
        </div>
        <div class="notification-content">
          <div class="notification-title">${notification.title}</div>
          <div class="notification-message">${notification.message}</div>
          <div class="notification-time">${this.formatTime(notification.createdAt)}</div>
        </div>
      </div>
    `).join('');
  }

  renderError(message) {
    if (this.notificationList) {
      this.notificationList.innerHTML = `<div class="notification-empty">${message}</div>`;
    }
  }

  updateBadge(count) {
    if (this.notificationBadge) {
      if (count > 0) {
        this.notificationBadge.textContent = count > 99 ? '99+' : count;
        this.notificationBadge.style.display = 'flex';
      } else {
        this.notificationBadge.style.display = 'none';
      }
    }
  }

  formatTime(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now - date) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Vừa xong';
    if (diffInMinutes < 60) return `${diffInMinutes} phút trước`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)} giờ trước`;
    return `${Math.floor(diffInMinutes / 1440)} ngày trước`;
  }

  getNotificationIcon(type) {
    const icons = {
      'new_order': 'fa-shopping-cart',
      'order_cancelled': 'fa-times-circle',
      'low_stock': 'fa-exclamation-triangle',
      'system': 'fa-info-circle'
    };
    return icons[type] || 'fa-bell';
  }

  async handleNotificationClick(notificationId, type, orderId) {
    // Mark as read first
    await this.markAsRead(notificationId);
    
    // Handle different notification types
    switch(type) {
      case 'new_order':
      case 'order_cancelled':
        if (orderId) {
          window.location.href = `/admin/order/view/${orderId}`;
        } else {
          window.location.href = '/admin/order';
        }
        break;
      case 'low_stock':
        window.location.href = '/admin/book';
        break;
      default:
        console.log('Clicked notification:', notificationId);
    }
    
    this.closeDropdown();
  }

  async markAsRead(notificationId) {
    try {
      const response = await fetch(`/api/notifications/${notificationId}/read`, {
        method: 'PUT'
      });

      if (response.ok) {
        // Update UI
        const item = document.querySelector(`[data-id="${notificationId}"]`);
        if (item) {
          item.classList.remove('unread');
        }
        this.updateUnreadCount();
      }
    } catch (error) {
      console.error('Lỗi đánh dấu đã đọc:', error);
    }
  }

  async markAllAsRead() {
    try {
      const response = await fetch('/api/notifications/mark-all-read', {
        method: 'PUT'
      });

      if (response.ok) {
        // Update UI
        const unreadItems = document.querySelectorAll('.notification-item.unread');
        unreadItems.forEach(item => item.classList.remove('unread'));
        this.updateBadge(0);
      }
    } catch (error) {
      console.error('Lỗi đánh dấu tất cả đã đọc:', error);
    }
  }

  async updateUnreadCount() {
    try {
      const response = await fetch('/api/notifications/unread-count');
      if (response.ok) {
        const data = await response.json();
        this.updateBadge(data.count || 0);
      }
    } catch (error) {
      console.error('Lỗi cập nhật số thông báo:', error);
    }
  }

  setupSocketListeners() {
    // Join admin room
    this.socket.emit('join-admin');
    
    // Listen for new notifications
    this.socket.on('new-notification', (notification) => {
      this.loadNotifications();
      this.showToast(`Thông báo mới: ${notification.title}`, 'info');
    });

    // Listen for notification updates
    this.socket.on('notification-update', () => {
      this.loadNotifications();
    });
  }

  showToast(message, type = 'info') {
    // Create toast notification
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.innerHTML = `
      <i class="fa-solid ${type === 'info' ? 'fa-info-circle' : 'fa-bell'}"></i>
      <span>${message}</span>
    `;
    
    // Add to page
    document.body.appendChild(toast);
    
    // Show toast
    setTimeout(() => toast.classList.add('show'), 100);
    
    // Remove toast after 3 seconds
    setTimeout(() => {
      toast.classList.remove('show');
      setTimeout(() => document.body.removeChild(toast), 300);
    }, 3000);
  }
}

// Initialize notification manager when page loads
let adminNotificationManager;
document.addEventListener('DOMContentLoaded', () => {
  adminNotificationManager = new AdminNotificationManager();
});

// Toast styles (inline for now, can be moved to CSS file)
const toastStyles = `
  .toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 12px 16px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    display: flex;
    align-items: center;
    gap: 8px;
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
  }
  
  .toast.show {
    transform: translateX(0);
  }
  
  .toast-info {
    border-left: 4px solid #2196f3;
  }
  
  .toast i {
    color: #2196f3;
  }
`;

// Add toast styles to page
const styleSheet = document.createElement('style');
styleSheet.textContent = toastStyles;
document.head.appendChild(styleSheet);
