doctype html
html(lang='vi')
  head
    meta(charset='UTF-8')
    meta(name='viewport' content='width=device-width, initial-scale=1.0')
    title ADMIN - WEB BÁN SÁCH TRUYỆN NHÓM 9
    link(rel='stylesheet' href='/public/css/admin.css')
    link(rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css')
    script(src='/socket.io/socket.io.js')
    style.
      /* Notification Styles */
      .notification-container {
        position: relative;
        display: inline-block;
        margin-right: 20px;
      }

      .notification-bell {
        background: none;
        border: none;
        color: white;
        font-size: 20px;
        cursor: pointer;
        position: relative;
        padding: 8px;
        border-radius: 50%;
        transition: all 0.3s ease;
      }

      .notification-bell:hover {
        background: rgba(255,255,255,0.1);
      }

      .notification-badge {
        position: absolute;
        top: 0;
        right: 0;
        background: #ff4757;
        color: white;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        font-size: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        animation: pulse 2s infinite;
      }

      @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
      }

      .notification-dropdown {
        position: absolute;
        top: 100%;
        right: 0;
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        width: 350px;
        max-height: 400px;
        overflow-y: auto;
        z-index: 1000;
        display: none;
      }

      .notification-dropdown.show {
        display: block;
        animation: slideDown 0.3s ease-out;
      }

      @keyframes slideDown {
        from {
          opacity: 0;
          transform: translateY(-10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .notification-header {
        padding: 15px;
        border-bottom: 1px solid #eee;
        background: #f8f9fa;
        border-radius: 8px 8px 0 0;
      }

      .notification-header h4 {
        margin: 0;
        color: #333;
        font-size: 16px;
      }

      .notification-actions {
        display: flex;
        gap: 10px;
        margin-top: 8px;
      }

      .btn-small {
        padding: 4px 8px;
        font-size: 12px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s ease;
      }

      .btn-mark-all {
        background: #007bff;
        color: white;
      }

      .btn-mark-all:hover {
        background: #0056b3;
      }

      .notification-list {
        max-height: 300px;
        overflow-y: auto;
      }

      .notification-item {
        padding: 12px 15px;
        border-bottom: 1px solid #eee;
        cursor: pointer;
        transition: all 0.2s ease;
        position: relative;
      }

      .notification-item:hover {
        background: #f8f9fa;
      }

      .notification-item.unread {
        background: #fff3cd;
        border-left: 4px solid #ffc107;
      }

      .notification-item.unread::before {
        content: '';
        position: absolute;
        top: 50%;
        right: 15px;
        transform: translateY(-50%);
        width: 8px;
        height: 8px;
        background: #007bff;
        border-radius: 50%;
      }

      .notification-title {
        font-weight: 600;
        color: #333;
        margin-bottom: 4px;
        font-size: 14px;
      }

      .notification-message {
        color: #666;
        font-size: 13px;
        line-height: 1.4;
        margin-bottom: 4px;
      }

      .notification-time {
        color: #999;
        font-size: 11px;
      }

      .notification-priority {
        display: inline-block;
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 10px;
        font-weight: bold;
        text-transform: uppercase;
        margin-left: 8px;
      }

      .priority-high {
        background: #ff6b6b;
        color: white;
      }

      .priority-urgent {
        background: #ff4757;
        color: white;
        animation: blink 1s infinite;
      }

      .priority-medium {
        background: #ffa502;
        color: white;
      }

      .priority-low {
        background: #70a1ff;
        color: white;
      }

      @keyframes blink {
        0%, 50% { opacity: 1; }
        51%, 100% { opacity: 0.5; }
      }

      .notification-empty {
        padding: 30px;
        text-align: center;
        color: #999;
      }

      .notification-footer {
        padding: 10px 15px;
        border-top: 1px solid #eee;
        background: #f8f9fa;
        text-align: center;
        border-radius: 0 0 8px 8px;
      }

      .notification-empty {
        padding: 30px;
        text-align: center;
        color: #999;
      }

      .notification-footer {
        padding: 10px 15px;
        border-top: 1px solid #eee;
        background: #f8f9fa;
        text-align: center;
        border-radius: 0 0 8px 8px;
      }

      .btn-view-all {
        color: #007bff;
        text-decoration: none;
        font-size: 13px;
        font-weight: 500;
      }

      .btn-view-all:hover {
        text-decoration: underline;
      }

      /* Toast notification */
      .toast-notification {
        position: fixed;
        top: 20px;
        right: 20px;
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 15px 20px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10000;
        max-width: 350px;
        animation: slideInRight 0.3s ease-out;
      }

      .toast-notification.new-order {
        border-left: 4px solid #28a745;
      }

      .toast-notification.order-cancelled {
        border-left: 4px solid #dc3545;
      }

      .toast-notification.low-stock {
        border-left: 4px solid #ffc107;
      }

      @keyframes slideInRight {
        from {
          transform: translateX(100%);
          opacity: 0;
        }
        to {
          transform: translateX(0);
          opacity: 1;
        }
      }

      @keyframes slideInRight {
        from {
          transform: translateX(100%);
          opacity: 0;
        }
        to {
          transform: translateX(0);
          opacity: 1;
        }
      }

      .toast-header {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
      }

      .toast-icon {
        margin-right: 8px;
        font-size: 16px;
      }

      .toast-title {
        font-weight: 600;
        color: #333;
        flex: 1;
      }

      .toast-close {
        background: none;
        border: none;
        font-size: 18px;
        color: #999;
        cursor: pointer;
        padding: 0;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .toast-close:hover {
        color: #666;
      }

      .toast-message {
        color: #666;
        font-size: 14px;
        line-height: 1.4;
      }
  body
    header
      .content
        ul.logo
          li
            h3 WEB BÁN SÁCH TRUYỆN NHÓM 9 - ADMIN
          li
            .notification-container
              button.notification-bell#notification-bell
                i.fa-solid.fa-bell
                span.notification-badge#notification-badge(style="display: none;") 0
              .notification-dropdown#notification-dropdown
                .notification-header
                  h4 Thông báo
                  .notification-actions
                    button.btn-small.btn-mark-all#mark-all-read Đánh dấu tất cả đã đọc
                .notification-list#notification-list
                  .notification-empty Không có thông báo mới
                .notification-footer
                  a.btn-view-all(href="#") Xem tất cả thông báo
            a(href='/auth/logout')
              | Đăng xuất
              i.fa-solid.fa-right-from-bracket
        ul.quanli
          li
            a(href='/admin')
              i.fa-solid.fa-house
              h4 Quản lí thông tin tổng quát
          li
            a(href='/admin/user')
              i.fa-solid.fa-circle-user
              h4 Quản lí người dùng
          li
            a(href='/admin/book')
              i.fa-solid.fa-book
              h4 Quản lí sách
          li
            a(href='/admin/category')
              i.fa-solid.fa-address-book
              h4 Quản lí danh mục
          li
            a(href='/admin/order')
              i.fa-solid.fa-cart-shopping
              h4 Quản lí đơn hàng
          li
            a(href='/admin/sale')
              i.fa-solid.fa-sack-dollar
              h4 Quản lí doanh thu
          li
            a(href='/')
              i.fa-solid.fa-share-from-square
              h4 Quay về trang chủ

    .main
      .admin
        h2 ADMIN/QUẢN LÍ

    section.slider
      .tongquat
        .tongso
          .tongso2
            i.fa-solid.fa-book
            span Tổng số quyển:
            span= totalBooks || '0'
          .tongso2
            i.fa-solid.fa-sack-dollar
            span Tổng số thu nhập:
            span= totalRevenue ? totalRevenue.toLocaleString() + 'đ' : '0đ'
        .tongso
          .tongso2
            i.fa-solid.fa-circle-user
            span Người dùng hệ thống:
            span= totalUsers || '0'
          .tongso2
            i.fa-solid.fa-truck-ramp-box
            span Tổng số đơn hàng trong tháng:
            span= totalOrders || '0'
    script(src='/public/js/admin.js')
    script.
      // Notification System
      class NotificationManager {
        constructor() {
          this.socket = io();
          this.notificationBell = document.getElementById('notification-bell');
          this.notificationBadge = document.getElementById('notification-badge');
          this.notificationDropdown = document.getElementById('notification-dropdown');
          this.notificationList = document.getElementById('notification-list');
          this.markAllReadBtn = document.getElementById('mark-all-read');

          this.init();
        }

        init() {
          // Join admin room
          this.socket.emit('join-admin');

          // Event listeners
          this.notificationBell.addEventListener('click', () => this.toggleDropdown());
          this.markAllReadBtn.addEventListener('click', () => this.markAllAsRead());

          // Socket events
          this.socket.on('new-notification', (notification) => {
            this.handleNewNotification(notification);
          });

          // Close dropdown when clicking outside
          document.addEventListener('click', (e) => {
            if (!e.target.closest('.notification-container')) {
              this.closeDropdown();
            }
          });

          // Load initial notifications
          this.loadNotifications();
          this.updateUnreadCount();
        }

        async loadNotifications() {
          try {
            const response = await fetch('/api/notifications');
            const data = await response.json();

            if (data.success) {
              this.renderNotifications(data.notifications);
              this.updateBadge(data.unreadCount);
            }
          } catch (error) {
            console.error('Lỗi tải thông báo:', error);
          }
        }

        async updateUnreadCount() {
          try {
            const response = await fetch('/api/notifications/unread-count');
            const data = await response.json();

            if (data.success) {
              this.updateBadge(data.unreadCount);
            }
          } catch (error) {
            console.error('Lỗi cập nhật số thông báo:', error);
          }
        }

        renderNotifications(notifications) {
          if (notifications.length === 0) {
            this.notificationList.innerHTML = '<div class="notification-empty">Không có thông báo mới</div>';
            return;
          }

          this.notificationList.innerHTML = notifications.map(notification => `
            <div class="notification-item ${!notification.isRead ? 'unread' : ''}"
                 data-id="${notification._id}"
                 onclick="notificationManager.markAsRead('${notification._id}')">
              <div class="notification-title">
                ${notification.title}
                <span class="notification-priority priority-${notification.priority}">${notification.priority}</span>
              </div>
              <div class="notification-message">${notification.message}</div>
              <div class="notification-time">${this.formatTime(notification.createdAt)}</div>
            </div>
          `).join('');
        }

        handleNewNotification(notification) {
          // Show toast
          this.showToast(notification);

          // Update badge
          this.updateUnreadCount();

          // Reload notifications if dropdown is open
          if (this.notificationDropdown.classList.contains('show')) {
            this.loadNotifications();
          }

          // Play notification sound (optional)
          this.playNotificationSound();
        }

        showToast(notification) {
          const toast = document.createElement('div');
          toast.className = `toast-notification ${notification.type}`;
          toast.innerHTML = `
            <div class="toast-header">
              <i class="toast-icon fa-solid ${this.getNotificationIcon(notification.type)}"></i>
              <div class="toast-title">${notification.title}</div>
              <button class="toast-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
            <div class="toast-message">${notification.message}</div>
          `;

          document.body.appendChild(toast);

          // Auto remove after 5 seconds
          setTimeout(() => {
            if (toast.parentNode) {
              toast.style.animation = 'slideInRight 0.3s ease-out reverse';
              setTimeout(() => toast.remove(), 300);
            }
          }, 5000);
        }

        getNotificationIcon(type) {
          const icons = {
            'new_order': 'fa-shopping-cart',
            'order_cancelled': 'fa-times-circle',
            'low_stock': 'fa-exclamation-triangle',
            'system': 'fa-info-circle'
          };
          return icons[type] || 'fa-bell';
        }

        async markAsRead(notificationId) {
          try {
            const response = await fetch(`/api/notifications/${notificationId}/read`, {
              method: 'PUT'
            });

            if (response.ok) {
              // Update UI
              const item = document.querySelector(`[data-id="${notificationId}"]`);
              if (item) {
                item.classList.remove('unread');
              }
              this.updateUnreadCount();
            }
          } catch (error) {
            console.error('Lỗi đánh dấu đã đọc:', error);
          }
        }

        async markAllAsRead() {
          try {
            const response = await fetch('/api/notifications/mark-all-read', {
              method: 'PUT'
            });

            if (response.ok) {
              // Update UI
              document.querySelectorAll('.notification-item.unread').forEach(item => {
                item.classList.remove('unread');
              });
              this.updateBadge(0);
            }
          } catch (error) {
            console.error('Lỗi đánh dấu tất cả đã đọc:', error);
          }
        }

        updateBadge(count) {
          if (count > 0) {
            this.notificationBadge.textContent = count > 99 ? '99+' : count;
            this.notificationBadge.style.display = 'flex';
          } else {
            this.notificationBadge.style.display = 'none';
          }
        }

        toggleDropdown() {
          if (this.notificationDropdown.classList.contains('show')) {
            this.closeDropdown();
          } else {
            this.openDropdown();
          }
        }

        openDropdown() {
          this.notificationDropdown.classList.add('show');
          this.loadNotifications();
        }

        closeDropdown() {
          this.notificationDropdown.classList.remove('show');
        }

        formatTime(dateString) {
          const date = new Date(dateString);
          const now = new Date();
          const diff = now - date;

          const minutes = Math.floor(diff / 60000);
          const hours = Math.floor(diff / 3600000);
          const days = Math.floor(diff / 86400000);

          if (minutes < 1) return 'Vừa xong';
          if (minutes < 60) return `${minutes} phút trước`;
          if (hours < 24) return `${hours} giờ trước`;
          if (days < 7) return `${days} ngày trước`;

          return date.toLocaleDateString('vi-VN');
        }

        playNotificationSound() {
          // Create a simple notification sound
          try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.value = 800;
            oscillator.type = 'sine';

            gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.5);
          } catch (error) {
            // Ignore audio errors
          }
        }
      }

      // Initialize notification manager when page loads
      let notificationManager;
      document.addEventListener('DOMContentLoaded', () => {
        notificationManager = new NotificationManager();
      });
