html
    head
        title T<PERSON><PERSON> k<PERSON>n
        link(rel="stylesheet", href="/public/css/main.css")
        link(rel="stylesheet", href="/public/css/style.css")
        link(rel="stylesheet", href="/public/css/responsive.css")
        link(rel="stylesheet", href="/public/css/col.css")
        script(src='/public/js/logout.js')
        link(rel="stylesheet", href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css")
        script(src='/public/js/addrForm.js')
    body
        include ../header.pug

        div(id="customer-header")
            //-Quay lại trang chủ
            div
                a(href="/customer")
                    button(class="fa-solid fa-arrow-left")
                P Tài khoản
        div(id="customer")
            div(class="customer-left")
                div
                    div
                        //-<PERSON><PERSON><PERSON> sửa từ thẻ a sang button
                        ul
                            li: a(href="/customer") Thông tin tài khoản
                            li(class="active"): a(href="/customer/address") Thông tin địa chỉ
                            li: a(href="/customer/change-password") Đổi mật khẩu
                            li: a(href="/customer/orders") Đơn hàng của bạn
                            li(class="active" data-user-id=user._id): a(href="/customer/notifications") Thông báo
                        button.log-out Đăng xuất
            div(class="customer-right")
                h1 Thông tin địa chỉ
                div
                    if error
                        div(class="alert-error")= error
                    form(class="customer-form", action="/customer/address/update", method="POST", id="address-form")
                        div(class="form-info")
                            p Họ và tên
                            input(type="text", name="name", placeholder="Họ và tên", value=user && user.address ? user.address.fullname : "", required)
                        div(class="form-info")
                            p Số điện thoại
                            input(type="text", name="phone", placeholder="Số điện thoại", value=user && user.address ? user.address.phone : "", required)
                        div(class="form-info")
                            p Quốc gia
                            input(type="text", name="country", placeholder="Quốc gia", value="Việt Nam", disabled)
                        div(class="form-info")
                            label(for="province") Tỉnh/Thành phố
                            select(id="province", name="city", required)
                                option(value="") Chọn Tỉnh/Thành phố
                                option(value="Hà Nội", selected=user && user.address && user.address.city === "Hà Nội", data-code="01") Hà Nội
                                option(value="TP. Hồ Chí Minh", selected=user && user.address && user.address.city === "TP. Hồ Chí Minh", data-code="79") TP. Hồ Chí Minh
                                option(value="Đà Nẵng", selected=user && user.address && user.address.city === "Đà Nẵng", data-code="48") Đà Nẵng
                        div(class="form-info")
                            label(for="district") Quận/Huyện
                            select(id="district", name="district", required)
                                option(value="") Chọn Quận/Huyện
                                if user && user.address && user.address.district
                                    option(value=user.address.district, selected)= user.address.district
                        div(class="form-info")
                            label(for="ward") Xã/Phường
                            select(id="ward", name="ward", required)
                                option(value="") Chọn Xã/Phường
                                if user && user.address && user.address.ward
                                    option(value=user.address.ward, selected)= user.address.ward
                        div(class="form-info")
                            p Địa chỉ
                            input(type="text", name="address", placeholder="Địa chỉ", value=user && user.address ? user.address.address : "", required)
                        div(class="form-info")
                            p Loại địa chỉ
                            div(class="radio-group")
                                div
                                    input(type="radio", id="home", name="addressType", value="home", checked=!user || !user.address || user.address.type !== "company")
                                    label(for="home") Nhà riêng / Chung cư
                                div
                                    input(type="radio", id="company", name="addressType", value="company", checked=user && user.address && user.address.type === "company")
                                    label(for="company") Cơ quan / Công ty
                        div(class="form-info")
                            button(class="create-update", type="submit") Cập nhật

        include ../logout.pug
        include ../suggest.pug
        include ../footer.pug

    script(src="/js/addrForm.js")
